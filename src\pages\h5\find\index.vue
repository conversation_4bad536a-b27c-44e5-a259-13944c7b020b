<template>
  <div class="mobile-container">
    <!-- 头部信息 -->
    <div
      class="w-full h-[1.08rem] flex justify-between bg-white fixed top-0 z-10 border-b-1 border-solid border-[#F2F2F2] px-[0.16rem] py-[0.36rem]"
    >
      <div class="flex items-center">
        <img
          loading="lazy"
          alt="back"
          class="w-[0.2rem] mr-[0.2rem]"
          @click="onBackClick"
          src="@/assets/icons/arrowLeft.svg"
          referrerpolicy="no-referrer"
        />
        <div class="text-center flex items-center">
          <div class="text-[0.36rem] leading-[0.36rem] font-medium">
            {{ authStore.i18n("cm_find.inquiryList") }}
          </div>
          <div
            v-if="pageData.stat?.skuCount"
            class="text-[0.28rem] leading-[0.28rem] ml-[0.08rem]"
          >
            ({{ pageData.stat?.skuCount }})
          </div>
        </div>
      </div>
      <div class="ml-[0.2rem] flex items-center">
        <img
          loading="lazy"
          alt="back"
          class="w-[0.24rem] mr-[0.08rem]"
          src="@/assets/icons/common/address.svg"
          referrerpolicy="no-referrer"
        />
        <mobile-country-select
          @save="onSaveCountry"
          spm="select_site_from_cart"
        />
      </div>
    </div>

    <div v-if="!pageData.loading">
      <div v-if="pageData.goodsList.length" class="pt-[0.16rem]">
        <!-- 二级 -->
        <div
          v-for="(goods, index) in pageData.goodsList"
          :key="goods.goodsId"
          class="mb-[0.5rem] px-[0.16rem]"
        >
          <div class="flex items-center w-full">
            <n-checkbox
              class="mr-[0.16rem]"
              v-model:checked="goods.selected"
              @update:checked="(value) => onGoodsSelection(value, goods)"
            >
            </n-checkbox>
            <a
              :href="`/h5/goods/${goods.goodsId}${
                goods.padc ? `?padc=${goods.padc}` : ''
              }`"
              data-spm-box="cart-goods-list"
              :data-spm-index="index + 1"
            >
              <mobile-goods-card
                :goods="goods"
                class="flex-1"
                spmCode="cart-goods-list"
                ><icon-card
                  name="uil:trash-alt"
                  color="#797979"
                  size="0.4rem"
                  class="mx-[0.16rem]"
                  @click.stop.prevent="onDeleteGoods(goods)"
                ></icon-card
              ></mobile-goods-card>
            </a>
          </div>
          <!-- 三级分类 -->
          <div
            v-for="sku in goods.skuList"
            :key="sku.skuId"
            class="mb-[0.24rem]"
          >
            <div class="flex items-center">
              <n-checkbox
                class="mr-[0.16rem]"
                v-model:checked="sku.selected"
                @update:checked="(value) => onSkuSelection(value, sku, goods)"
              >
              </n-checkbox>
              <mobile-sku-card
                :sku="sku"
                :goods="goods"
                @onCartQtyUpdate="onCartQtyUpdate"
                :step="sku.minIncreaseQuantity"
                ><template v-slot:spec>
                  <icon-card
                    name="iconamoon:arrow-right-2"
                    color="#797979"
                    size="0.45rem"
                    @click.stop="onOpenSkuDialog(sku, goods)"
                  ></icon-card> </template
                ><template v-slot:delete
                  ><icon-card
                    name="uil:trash-alt"
                    color="#797979"
                    size="0.4rem"
                    @click="onDeleteSku(sku, goods)"
                  ></icon-card></template
              ></mobile-sku-card>
            </div>
          </div>
        </div>

        <!-- 底部信息 -->
        <div
          class="w-full fixed bottom-[1.32rem] bg-white pt-[0.24rem] pb-[0.32rem] px-[0.16rem]"
        >
          <div>
            <div class="flex items-start justify-between">
              <!-- 一级 -->
              <n-checkbox
                v-model:checked="pageData.selectAll"
                @update:checked="onAllSelection"
                ><span class="text-[0.32rem] leading-[0.32rem]">{{
                  authStore.i18n("cm_find_selectAll")
                }}</span></n-checkbox
              >
              <div class="flex gap-[0.08rem] items-center">
                <span class="text-[0.28rem] leading-[0.28rem]">
                  {{ authStore.i18n("cm_find_itemsCost") }}:
                </span>
                <div
                  class="text-[0.36rem] leading-[0.36rem] font-medium flex-shrink-0 flex-1"
                  v-if="
                    pageData.stat.selectTotalSalePrice ||
                    pageData.stat.selectTotalSalePrice === 0
                  "
                >
                  <span
                    class="text-[0.32rem] leading-[0.32rem] font-medium mr-[0.04rem]"
                  >
                    {{ monetaryUnit }} </span
                  >{{ setNewUnit(pageData.stat.selectTotalSalePrice, true) }}
                </div>
                <icon-card
                  color="#555"
                  name="ri:arrow-down-s-line"
                  size="26"
                  @click="openInquireTip"
                  class="hidden"
                />
              </div>
            </div>
            <div
              class="flex text-[0.28rem] leading-[0.28rem] items-center justify-end mt-[0.12rem] text-[#7F7F7F]"
            >
              <n-popover trigger="hover" raw>
                <template #trigger>
                  <div
                    class="cursor-pointer flex-shrink-0 mr-[0.06rem] text-[#7F7F7F] flex items-center"
                  >
                    <img
                      class="w-[0.28rem] h-[0.28rem] mr-[0.04rem]"
                      src="@/assets/icons/common/alert-circle.svg"
                      :alt="authStore.i18n('cm_goods.estimatedShippingCost')"
                      referrerpolicy="no-referrer"
                    />
                    {{ authStore.i18n("cm_goods.estimatedShippingCost") }}:
                  </div>
                </template>
                <div
                  style="
                    z-index: 1;
                    width: 300px;
                    padding: 6px 14px;
                    background-color: #fff4d4;
                    transform-origin: inherit;
                    border: 1px solid #f7ba2a;
                  "
                >
                  {{ authStore.i18n("cm_goods.freightAdjustmentPending") }}
                </div>
              </n-popover>

              <span
                class="flex-shrink-0"
                v-if="pageData?.totalEstimateFreight"
                >{{ setUnit(pageData.totalEstimateFreight) }}</span
              >
              <span v-else class="flex-shrink-0">{{
                authStore.i18n("cm_goods.pendingConfirmation")
              }}</span>
            </div>
          </div>
          <n-button
            block
            size="large"
            color="#E50113"
            text-color="#fff"
            class="rounded-[0.16rem] h-[0.92rem] mt-[0.32rem]"
            @click="onGoFindSubmit($event)"
            data-spm-box="cart-to-checkout"
          >
            <div>
              <div class="text-[0.32rem] leading-[0.32rem] font-medium">
                {{ authStore.i18n("cm_find.inquireNow") }}
              </div>
              <div class="text-[0.28rem] leading-[0.28rem] mt-[0.04rem]">
                {{ authStore.i18n("cm_find_confirmWithoutPay") }}
              </div>
            </div>
          </n-button>
        </div>
      </div>
      <n-empty
        v-else
        :description="authStore.i18n('cm_find.noData')"
        class="mt-[1.92rem] text-[0.28rem]"
      >
        <template #extra>
          <n-button
            size="small"
            color="#E50113"
            text-color="#fff"
            @click="onGoHome"
            class="text-[0.28rem] h-[0.56rem] px-[0.2rem]"
          >
            {{ authStore.i18n("cm_find.goHome") }}</n-button
          >
        </template>
      </n-empty>
    </div>

    <div v-show="pageData.loading" class="loading-overlay">
      <n-spin stroke="#e50113" :show="pageData.loading"> </n-spin>
    </div>

    <mobile-tab-bar :naiveBar="3" />

    <n-drawer
      v-model:show="pageData.dialogVisible"
      resizable
      default-width="100%"
      default-height="90%"
      placement="bottom"
      :on-after-leave="onCancel"
    >
      <n-drawer-content>
        <template #header></template>
        <!-- 商品规格 -->
        <div>
          <div
            v-for="spec in pageData.currentGoods.specList"
            :key="spec.id"
            class="ml-[0.16rem] text-[0.28rem]"
          >
            <div class="mb-[0.08rem] font-medium">{{ spec.name }}:</div>
            <div class="flex flex-wrap mb-[0.16rem]">
              <div
                v-for="item in spec.items"
                :key="item.itemId"
                @click="onSpecUpdate(spec.id, item.itemId, item)"
                class="spec-btn min-w-[0.8rem] max-w-4/5 relative"
                :class="{
                  'current-btn': pageData.selectedSpec[spec?.id] == item.itemId,
                  'disabled-btn': item.disabled,
                }"
              >
                <n-image
                  lazy
                  preview-disabled
                  object-fit="fill"
                  class="w-[0.72rem] h-[0.72rem] shrink-0 mr-[0.24rem]"
                  :src="item.imageUrl"
                  v-if="item.imageUrl"
                  :img-props="{ referrerpolicy: 'no-referrer' }"
                />
                <span class="py-[0.16rem]">{{ item.itemName }}</span>
                <icon-card
                  size="20"
                  name="typcn:tick"
                  color="#fff"
                  class="btn-tick mr-[0.16rem]"
                ></icon-card>
              </div>
            </div>
          </div>
        </div>
        <template #footer
          ><div class="flex w-full">
            <n-button
              @click="onConfirm"
              color="#E50113"
              class="mr-[0.32rem] flex-1 text-[0.28rem] h-[0.68rem]"
              >{{ authStore.i18n("cm_find.confirm") }}</n-button
            >
            <n-button
              @click="onCancel"
              class="flex-1 text-[0.28rem] h-[0.68rem]"
              >{{ authStore.i18n("cm_find.cancel") }}</n-button
            >
          </div></template
        >
      </n-drawer-content>
    </n-drawer>

    <n-drawer
      v-model:show="pageData.showTipDrawer"
      width="100%"
      placement="bottom"
      :trap-focus="false"
      default-height="4.4rem"
    >
      <n-drawer-content closable>
        <template #header>
          <div
            class="text-[0.32rem] leading-[0.48rem] font-medium mb-[0.32rem] pt-[0.32rem]"
          >
            {{ authStore.i18n("cm_find_inquireSummary") }} (<span>
              {{ pageData.stat?.selectSkuTotalQuantity }}</span
            >
            {{ authStore.i18n("cm_find_ items") }})
          </div>
        </template>
        <div class="mb-[0.48rem]">
          <div
            class="mb-[0.16rem] flex justify-between text-[0.28rem] font-medium"
          >
            <span class="text-gray-500">{{
              authStore.i18n("cm_find_itemsCost")
            }}</span
            ><span class="text-[#2D2D2D]">{{
              setUnit(pageData.stat.selectTotalSalePrice)
            }}</span>
          </div>
          <div
            class="bg-[rgba(247,186,42,.1)] mt-[0.48rem] px-[0.16rem] py-[0.08rem] flex text-[0.26rem]"
          >
            <icon-card
              size="0.4rem"
              name="mingcute:warning-line"
              color="#F7BA2A"
              class="mr-[0.08rem]"
            ></icon-card>
            <span>
              {{ authStore.i18n("cm_find_submitTip") }}
            </span>
          </div>
        </div>
      </n-drawer-content></n-drawer
    >
    <!-- 询盘提交错误提示 -->
    <n-modal :show="pageData.errorDialogVisible" :show-icon="false">
      <n-card style="width: 7.2rem" :bordered="false">
        <div>
          <icon-card
            size="0.45rem"
            name="mingcute:warning-line"
            class="add-btn-check"
            color="#E50113"
          ></icon-card>
          {{ pageData.errorMessage }}
        </div>
        <n-button
          size="small"
          color="#E50113"
          text-color="#fff"
          round
          @click="onGoHome"
          class="mt-[0.32rem]"
        >
          {{ authStore.i18n("cm_find_shopAgain") }}</n-button
        >
      </n-card>
    </n-modal>
    <!-- 2000美元提交校验提示 -->
    <n-modal :show="pageData.submitDialogVisible" :show-icon="false">
      <n-card
        :bordered="false"
        style="
          width: 7.1rem;
          color: #000;
          padding: 0 0.2rem !important;
          text-align: center;
        "
      >
        <div class="text-[0.3rem] leading-[0.42rem] mb-[0.4rem] px-[0.02rem]">
          El importe de su producto es inferior a US$ 2000,
          <span class="text-[#e50113]"
            >los gastos de envío pueden ser más caros que el coste del
            producto</span
          >, se recomienda aumentar la compra a US$ 2000.
        </div>
        <div class="flex justify-between">
          <n-button
            round
            color="#fff"
            text-color="#000"
            data-spm-box="cart-to-checkout"
            @click="onConfirmSubmit($event, 'dialog')"
            class="border-btn w-[3.12rem] h-[0.68rem] p-0 text-[0.28rem]"
          >
            {{ authStore.i18n("cm_common.buySubmit") }}</n-button
          >
          <n-button
            round
            color="#E50113"
            text-color="#fff"
            class="w-[3.12rem] h-[0.68rem] p-0 text-[0.28rem]"
            @click="onCancelSubmit"
          >
            {{ authStore.i18n("cm_common.buyAgain") }}</n-button
          >
        </div>
      </n-card>
    </n-modal>
    <!-- 生成订单loading -->
    <SubmitLoadingModal :show="pageData.submitLoading" />
  </div>
</template>
<script setup lang="ts">
const route = useRoute();
const router = useRouter();
const message = useMessage();
import { useAuthStore } from "@/stores/authStore";

import MobileSkuCard from "./components/MobileSkuCard.vue";
import MobileGoodsCard from "./components/MobileGoodsCard.vue";
import MobileStepCard from "./components/MobileStepCard.vue";
import SubmitLoadingModal from "@/pages/find/components/SubmitLoadingModal.vue";

const authStore = useAuthStore();
const pageData = reactive<any>({
  selectAll: false,
  showTipDrawer: false,
  dialogVisible: false,
  errorDialogVisible: false,
  submitDialogVisible: false,
  errorMessage: "",
  goodsList: <any>[],
  currentGoods: <any>{},
  currentSku: <any>{},
  updatedSku: <any>{},
  selectedSpec: <any>{},
  totalEstimateFreight: null, //预估运费总计（全部SKU可预估运费时，才返回）
  loading: false,
  submitLoading: false,
});

watch(
  () => pageData.selectedSpec,
  (newVal: any) => {
    // 多组规格时 选中倒数第二组的时候 需要检验sku的库存以及上下架状态
    if (
      newVal &&
      pageData.currentGoods?.specList?.length > 1 &&
      Object.values(newVal).length > pageData.currentGoods?.specList.length - 2
    ) {
      const targetId =
        pageData.currentGoods?.specList[
          pageData.currentGoods?.specList?.length - 2
        ].id;
      for (const id in pageData.selectedSpec) {
        if (targetId == id) {
          updateSpecStatus();
          break;
        }
      }
    } else {
      //只有一组时 直接校验sku的库存以及上下架状态
      updateOneSpecStatus();
    }
  },
  { deep: true }
);

onGetCart("init");
// 获取购物车列表
async function onGetCart(type?: any) {
  if (type == "init") {
    pageData.loading = true;
  }
  const res: any = await useGetCart({});
  if (type == "init") {
    pageData.loading = false;
  }
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    if (res?.data?.totalEstimateFreight) {
      pageData.totalEstimateFreight = res?.data.totalEstimateFreight;
    } else {
      pageData.totalEstimateFreight = null;
    }
    // 商品选中
    pageData.goodsList.forEach((goods: any) => {
      goods.selected = goods.skuList.every((obj: any) => obj.selected);
      goods.skuTotalQuantity = 0; //sku总数量
      goods.skuSelectedQuantity = 0; // 选中的sku总数量
      goods.skuList.forEach((sku: any) => {
        // sku上没有最小加购数量时，则取商品的最小加购数量
        if (!sku.minIncreaseQuantity) {
          sku.minIncreaseQuantity = goods.minIncreaseQuantity;
        }
        goods.skuTotalQuantity += sku.buyQty;
        if (sku.selected) {
          goods.skuSelected = true; // sku是否选中
          goods.skuSelectedQuantity += sku.buyQty;
        }
      });
    });
    // 全选
    pageData.selectAll = pageData.goodsList.every(
      (goods: any) => goods.selected
    );
    authStore.getCartList(res?.data); // 同步更新store的购物车数据
  } else if (res?.result?.code === 403) {
    //未登录 去登录
    window.location.href = `/h5/user/login?pageSource=${window.location.href}`;
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

// 全选
async function onAllSelection(value: any) {
  onUpdateCart({ selectedAll: value });
}
// 商品选中
function onGoodsSelection(value: any, goods: any) {
  onUpdateCart({ selected: value, goodsId: goods.goodsId, padc: goods.padc });
}
// sku选中
function onSkuSelection(value: any, sku: any, goods: any) {
  onUpdateCart({ selected: value, skuId: sku.skuId, padc: sku.padc });
}
// sku数量修改
function onCartQtyUpdate(value: any, sku: any, goods: any) {
  onUpdateCart({
    quantity: value,
    selected: sku.selected,
    skuId: sku.skuId,
    padc: sku.padc,
  });
}
// 修改
async function onUpdateCart(params: any) {
  const res: any = await useUpdateCart(params);
  if (res?.result?.code === 200) {
    onGetCart();
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
// 删除商品
async function onDeleteGoods(goods: any) {
  onRemoveCart({ goodsId: goods.goodsId, padc: goods.padc });
}
// 删除sku
async function onDeleteSku(sku: any, goods: any) {
  onRemoveCart({ skuId: sku.skuId, padc: sku.padc });
}
// 删除
async function onRemoveCart(params: any) {
  const res: any = await useRemoveCart(params);
  if (res?.result?.code === 200) {
    message.success(authStore.i18n("cm_find.deleteSuccessMessage"), {
      duration: 3000,
    });
    onGetCart();
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
function onGoFindSubmit(event: any) {
  window?.MyStat?.addPageEvent("click_start_looking", `点击创建询盘按钮`, true); // 埋点
  if (pageData.stat?.selectTotalSalePrice < 2000) {
    pageData.submitDialogVisible = true;
    window?.MyStat?.addPageEvent(
      "less_then_2000usd_dialog_open",
      `未满2000美元对话框-打开`
    ); // 埋点
    return;
  }
  onConfirmSubmit(event);
}

async function onConfirmSubmit(event: any, from?: any) {
  pageData.submitLoading = true;
  if (from) {
    window?.MyStat?.addPageEvent(
      "less_then_2000usd_dialog_ingore",
      `未满2000美元对话框-忽略`
    ); // 埋点
  }
  window?.MyStat?.addPageEvent(
    "carrito_click_inquiry",
    `购物车列表进入询盘提交页`
  ); // 埋点

  const selectedSkuList = <any>[];
  pageData.goodsList?.forEach((goods: any) => {
    goods.skuList.forEach((sku: any) => {
      if (sku.selected) {
        selectedSkuList.push({
          quantity: sku.buyQty,
          skuId: sku.skuId,
          spm: sku.spm,
          routeId: goods.routeId,
          padc: sku.padc,
        });
      }
    });
  });
  if (!!window?.fbq) {
    window?.fbq("track", "InitiateCheckout", {
      currency: "USD",
      num_items: selectedSkuList?.length,
      contents: selectedSkuList,
    });
  }
  if (!!window?.ttq) {
    window?.ttq?.track("InitiateCheckout", {
      currency: "USD",
      value: pageData?.stat?.selectTotalSalePrice,
      content_type: "product",
      description: JSON.stringify(selectedSkuList),
    });
  }

  const res: any = await useGetInquiry({
    params: selectedSkuList,
    siteId: window.siteData.siteInfo.id,
  });
  if (res?.result?.code === 200) {
    const inquiryInfo = res?.data;
    await authStore.setInquiryInfo(inquiryInfo);
    const url = navigateUrl(`/h5/find/submit`, {}, event);
    window.location.replace(url);
  } else {
    window?.MyStat?.addPageEvent(
      "submit_start_looking_error",
      `进入询盘信息页错误：${res?.result?.message}`
    ); // 埋点
    pageData.errorDialogVisible = true;
    setTimeout(() => {
      pageData.errorDialogVisible = false;
    }, 3000);
    pageData.errorMessage =
      res?.result?.message || authStore.i18n("cm_find.errorMessage");
  }
  pageData.submitLoading = false;
}

function onCancelSubmit() {
  window?.MyStat?.addPageEvent(
    "less_then_2000usd_dialog_close",
    `未满2000美元对话框-关闭`
  ); // 埋点
  pageData.submitDialogVisible = false;
}

async function onOpenSkuDialog(sku: any, goods: any) {
  const res: any = await useGoodsInfo({
    id: goods.goodsId,
    padc: sku.padc,
    deviceType: 1,
  });
  if (res?.result?.code === 200) {
    pageData.currentSku = sku;
    pageData.updatedSku = sku;
    pageData.currentGoods = res?.data;
    await onInitGoodsData();
    pageData.dialogVisible = true;
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
async function onInitGoodsData() {
  pageData.selectedSpec = {};
  pageData.currentSku.specItemList.forEach((spec: any) => {
    pageData.selectedSpec[spec.groupId] = spec.itemId;
  });
}
function onSpecUpdate(specId: string, itemId: string, item: any) {
  if (item.disabled) return;
  pageData.selectedSpec[specId] = itemId;
}
// 更新最后一组规格组的状态 最后一组规格会校验sku的库存以及下架状态 如果没有查到sku 默认下架
function updateSpecStatus() {
  // 将对象转成数组 方便处理
  let selectList = <any>[];
  for (const id in pageData.selectedSpec) {
    selectList.push(pageData.selectedSpec[id]);
  }
  // 这里需要将选中的最后一组的规格给移除掉 再去找匹配的sku
  const lastSpecList =
    pageData.currentGoods.specList[pageData.currentGoods.specList.length - 1];
  lastSpecList.items.forEach((item: any) => {
    if (selectList.includes(item.itemId)) {
      const existingIndex = selectList.indexOf(item.itemId);
      if (existingIndex !== -1) {
        selectList.splice(existingIndex, 1);
      }
    }
  });
  lastSpecList.items.forEach((t: any) => {
    t.disabled = true;
  });
  // 获取所有满足条件的SKU
  const matchingSkus = pageData.currentGoods.skuList.filter((sku: any) => {
    return selectList.every((specItemId: string) =>
      sku.specItems.some(
        (skuSpecItem: any) => skuSpecItem.itemId === specItemId
      )
    );
  });
  // 如果有满足条件的SKU，则更新规格的可选状态
  if (matchingSkus.length > 0) {
    matchingSkus.forEach((sku: any) => {
      let disabled = true;
      // 判断当前选中的sku是否是当前sku 如果是则还是可选的
      if (sku.id == pageData.currentSku?.skuId && sku.stockQty != 0) {
        disabled = false;
      }
      // 如果库存为0 则不可选 如果cartQty选购的数量大于0 则是已加购的规格 也是不可选的
      if (sku.stockQty != 0 && sku.cartQty == 0) {
        disabled = false;
      }
      const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
      lastSpecList.items.forEach((item: any) => {
        if (item.itemId == matchingItemId) {
          item.disabled = disabled;
        }
      });
    });
  } else {
    lastSpecList.items.forEach((item: any) => {
      item.disabled = true;
    });
  }
  onFilterSku();
}
// 只有一个规格时 判断当前规格的sku有没有库存以及上下架
function updateOneSpecStatus() {
  const onlyOneSpecItems = pageData.currentGoods?.specList[0].items;
  onlyOneSpecItems.forEach((t: any) => {
    t.disabled = true;
    const matchingSkus = pageData.currentGoods?.skuList.filter((sku: any) => {
      return sku.specItems.some(
        (skuSpecItem: any) => skuSpecItem.itemId === t.itemId
      );
    });
    if (matchingSkus.length > 0) {
      matchingSkus.forEach((sku: any) => {
        let disabled = true;
        // 判断当前选中的sku是否是当前sku 如果是则还是可选的
        if (sku.id == pageData.currentSku?.skuId && sku.stockQty != 0) {
          disabled = false;
        }
        if (sku.stockQty != 0 && sku.cartQty == 0) {
          disabled = false;
        }
        const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
        onlyOneSpecItems.forEach((item: any) => {
          if (item.itemId == matchingItemId) {
            item.disabled = disabled;
          }
        });
      });
    } else {
      onlyOneSpecItems.forEach((item: any) => {
        item.disabled = true;
      });
    }
  });
  onFilterSku();
}

// 筛选出sku
function onFilterSku() {
  const lastSpecList =
    pageData.currentGoods.specList[pageData.currentGoods.specList.length - 1];
  // 选中的sku是下架或者没有库存的情况下 将最后一组规格里选中规格的取消
  for (const id in pageData.selectedSpec) {
    lastSpecList.items.forEach((item: any) => {
      if (item.itemId == pageData.selectedSpec[id] && item.disabled) {
        delete pageData.selectedSpec[id];
      }
    });
  }
  const skus = pageData.currentGoods.skuList.filter((sku: any) => {
    return (
      Object.entries(pageData.selectedSpec).every(([specId, itemId]) => {
        const matchingSpec = sku.specItems.find(
          (spec: any) => spec.specId === specId
        );
        if (!matchingSpec || matchingSpec.itemId !== itemId) {
          return false;
        }
        return true;
      }) && Object.keys(pageData.selectedSpec).length === sku.specItems.length
    );
  });
  pageData.updatedSku = skus.length > 0 ? skus[0] : null;
}
async function onConfirm() {
  const params = {
    selected: true,
    updatedSkuId: pageData.updatedSku.id,
    skuId: pageData.currentSku.skuId,
    goodsId: pageData.currentGoods.id,
    padc: pageData.currentSku.padc,
  };
  const res: any = await useUpdateCart(params);
  if (res?.result?.code === 200) {
    pageData.dialogVisible = false;
    onGetCart();
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
function onCancel() {
  pageData.dialogVisible = false;
}

function openInquireTip() {
  pageData.showTipDrawer = !pageData.showTipDrawer;
}

// 返回
function onBackClick() {
  router.go(-1);
}

function onGoHome() {
  window.location.href = "/h5";
}

// 保存选择的国家
const onSaveCountry = () => {
  // 可以根据需要决定是否重新加载页面
  window.location.reload();
};
</script>
<style scoped lang="scss">
.mobile-container {
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
  padding-top: 1.08rem;
  padding-bottom: 3.8rem;
}

:deep(.n-checkbox.n-checkbox--checked .n-checkbox-box) {
  background: #e50113;
}
:deep(.n-checkbox__label) {
  width: 100%;
  padding-left: 0.12rem;
  padding-right: 0.12rem;
}
:deep(.divider .n-divider) {
  margin: 0;
}
.divider.n-divider:not(.n-divider--vertical) {
  margin: 0;
}
:deep(.divider .n-divider__line.n-divider__line--left) {
  display: none;
}
.spec-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 0.8rem;
  margin: 0.13rem 0.32rem 0.13rem 0;
  padding: 0 0.35rem;
  min-width: 0.8rem;
  cursor: pointer;
  color: #797979;
  border-radius: 0.1rem;
  border: 0.02rem solid #d7d7d7;
}
.disabled-btn {
  background: #d7d7d7;
  cursor: not-allowed;
  border: none;
}
.current-btn {
  color: #e50113;
  cursor: pointer;
  border: none;
  background: rgba(229, 1, 19, 0.18);
  .btn-tick {
    opacity: 1;
  }
}
.btn-tick {
  position: absolute;
  right: -0.2rem;
  bottom: 0;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.btn-tick::before {
  content: "";
  position: absolute;
  top: 0.1rem;
  right: 0.04rem;
  bottom: 0.02rem;
  left: 0.02rem;
  background-color: #e50113;
  z-index: -1;
  border-radius: 0.16rem 0 0.1rem 0;
}
:deep(.n-drawer-header) {
  padding-top: 0 !important;
  padding-left: 0.2rem !important;
  padding-right: 0.2rem !important;
  border: none !important;
}
:deep(.n-drawer-footer) {
  border: none !important;
}
:deep(.n-drawer-body-content-wrapper) {
  padding: 0 0.2rem !important;
}
:deep(.n-card__content) {
  color: #000;
  padding: 0.48rem 0 !important;
  text-align: center;
}

.border-btn {
  border: 0.02rem solid #c7c7c7;
  &:hover {
    background: #e50113;
    color: #fff;
    border: none;
  }
}
.loading-overlay {
  position: fixed;
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
}
:deep(.country-delivery) {
  display: none;
}
:deep(.country-code) {
  font-size: 0.28rem;
  line-height: 0.28rem;
}
</style>
