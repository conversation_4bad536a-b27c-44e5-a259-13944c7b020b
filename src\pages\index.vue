<template>
  <!-- SEO 部分 -->
  <seo-data :pageData="pageData"></seo-data>

  <!-- 已登录用户部分 -->
  <template v-if="userInfo?.username">
    <page-layout :categories="pageData.category">
      <template #main-content>
        <div class="carousel-wrapper h-[478px]">
          <n-carousel
            autoplay
            :transition-style="{ transitionDuration: '500ms' }"
            style="max-height: 478px"
          >
            <n-image
              lazy
              preview-disabled
              class="carousel-img rounded-[20px]"
              :src="carousel"
              v-for="(carousel, index) in carouselData"
              :key="index"
              :img-props="{ referrerpolicy: 'no-referrer' }"
            />
          </n-carousel>
        </div>
        <!-- 推荐资讯 -->
        <div
          class="news-wrapper mt-[24px] mb-[40px] flex justify-between items-center bg-[#F2F2F2] py-[16px] px-[20px] rounded-[20px]"
          data-spm-box="homepage-welcome-bar"
        >
          <div class="text-[16px] leading-[16px]">
            {{ authStore.i18n("cm_home.welcomeChilat") }}
          </div>
          <div class="flex">
            <n-space :style="{ gap: '0 20px' }"
              ><a
                class="hover:underline cursor-pointer flex items-center"
                v-for="(news, index) in newsData"
                :key="index"
                :href="news.path"
                target="_blank"
              >
                <icon-card
                  :name="news.icon"
                  size="24"
                  color="#db2221"
                ></icon-card>
                <span class="ml-[4px]">{{ news.title }}</span>
              </a></n-space
            >
          </div>
        </div>
        <div
          class="flex flex-col gap-[60px] mb-[60px]"
          v-if="pageData.customerExclusiveActivities.length"
        >
          <category-carousal
            data-spm-box="homepage-exclusive-activity"
            v-for="(item, index) in pageData.customerExclusiveActivities"
            :key="index"
            :selector="item"
          ></category-carousal>
        </div>
        <!-- 热销货盘 -->
        <hot-selectors
          :selectorList="pageData.selectorList"
          :recommendSupplierGoods="pageData.recommendSupplierGoods"
          :habitableCapsuleGoods="pageData.habitableCapsuleGoods"
        ></hot-selectors>

        <!-- 货盘分类 -->
        <div>
          <div class="text-[28px] leading-[28px] font-medium mb-[50px]">
            {{ authStore.i18n("cm_home.popularCategories") }}
          </div>
          <!-- 推荐货盘 -->
          <category-wrapper
            spmCode="homepage-recommend-tag"
            :categoryGoods="pageData.tagGoodsList"
          />
          <!-- 推荐分类 -->
          <!-- <category-wrapper
            spmCode="homepage-recommend-category"
            :categoryGoods="pageData.categoryGoods"
          /> -->
        </div>
      </template>
    </page-layout>
  </template>

  <!-- 游客部分 -->
  <template v-else>
    <guest-home
      :hotKeywords="pageData.hotKeywords"
      v-if="abtestMode === 'A'"
    ></guest-home>
    <new-guest-home
      :categories="pageData.category"
      :hotKeywords="pageData.hotKeywords"
      :hotSaleGoods="pageData.hotSaleGoods"
      :selectorList="pageData.selectorList"
      :recommendSupplierGoods="pageData.recommendSupplierGoods"
      :habitableCapsuleGoods="pageData.habitableCapsuleGoods"
      v-else
    ></new-guest-home>
  </template>

  <!-- <holiday-notice-modal></holiday-notice-modal> -->
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import GuestHome from "./components/GuestHome.vue";
import NewGuestHome from "./components/NewGuestHome.vue";
import CategoryWrapper from "./components/CategoryWrapper.vue";
import homeCarouselThousands from "@/assets/icons/home/<USER>";
import homeCarouselDiscount from "@/assets/icons/home/<USER>";
import homeCarouselFactories from "@/assets/icons/home/<USER>";
import homeCarouselUnique from "@/assets/icons/home/<USER>";
import homeCarouselReward from "@/assets/icons/home/<USER>";
// import homeCarouselLive from "@/assets/icons/home/<USER>";

import HotSelectors from "./components/HotSelectors.vue";
import CategoryCarousal from "./components/CategoryCarousal.vue";

const nuxtApp = useNuxtApp();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const pageData = reactive(<any>{
  seoData: {},
  hotSaleGoods: {},
  tagGoodsList: [], //货盘信息
  selectorList: [], //热销货盘
  recommendSupplierGoods: {}, //“优质供应商”商品推荐
  customerExclusiveActivities: [], //客户专属活动
  habitableCapsuleGoods: {},
});

// 定义用户信息接口，解决类型错误
interface UserInfo {
  username?: string;
  [key: string]: any;
}

const userInfo = ref<UserInfo>({});
const abtestMode = ref<string>("");
abtestMode.value = config.public.abtestMode as string;
userInfo.value = config.public.userInfo as UserInfo;

/**
 * 为了解决轮播图在服务端渲染到浏览器渲染时，从最后一张切换到第一张可能出现的视觉跳动问题：
 * - 服务端渲染时在尾部增加首张图片
 * - 浏览器渲染时移除尾部的重复图片
 */
const carouselData = [
  // homeCarouselLive,
  homeCarouselFactories,
  homeCarouselDiscount,
  homeCarouselUnique,
  homeCarouselReward,
  homeCarouselThousands,
  homeCarouselFactories,
];

const newsData = [
  {
    icon: "mdi:information-variant-circle",
    title: authStore.i18n("cm_news.aboutUs"),
    path: "/article/about-us",
  },
  {
    icon: "mdi:compass",
    title: authStore.i18n("cm_news.quickGuide"),
    path: "/article/quick-guide",
  },
  {
    icon: "material-symbols:help",
    title: authStore.i18n("cm_news.askedQuestions"),
    path: "/article/frequently-questions",
  },
  // {
  //   icon: "material-symbols:security",
  //   title: authStore.i18n("cm_news.warrantyService"),
  //   path: `/article?code=10002`,
  // },
  {
    icon: "f7:money-dollar-circle-fill",
    title: authStore.i18n("cm_news.invitedReward"),
    path: "/article/invite",
  },
];

await onHomePageData();
onCategoryGoods();

onBeforeMount(() => {
  // 初始化轮播图数据数组，确保首尾没有重复元素
  if (
    carouselData.length > 1 &&
    carouselData[0] === carouselData[carouselData.length - 1]
  ) {
    carouselData.pop();
  }
});

onMounted(() => {
  authStore.setFromInviteCode(); //存储分享链接上的邀请码
});

async function onHomePageData() {
  const HOME_CAPSULE_PRODUCT_ID =
    useRuntimeConfig().public.HOME_CAPSULE_PRODUCT_ID;
  const [home, recommend]: any = await Promise.all([
    useHomePageData({}),
    useRecommendGoodsV2({
      goodsCount: 7,
      deviceType: 1,
      siteId: window?.siteData?.siteInfo?.id,
      goodsPlugTagParams: [
        {
          tagId: HOME_CAPSULE_PRODUCT_ID,
        },
      ],
    }),
  ]);

  if (home?.result?.code === 200) {
    nuxtApp.$setResponseHeaders(pageData.seoData?.responseHeaders);
    pageData.hotKeywords = home?.data.hotKeywords;
    pageData.seoData = home?.data.seoData;
    // 优先从首页接口获取 拿不到则从分类接口去拿
    pageData.category = home?.data?.categoryTree?.children;
    if (!pageData.category) {
      pageData.category = await authStore.getCategoryTree();
    }
  }
  if (recommend?.result?.code === 200) {
    pageData.hotSaleGoods = recommend.data?.hotSaleGoods;
    // 热销货盘
    pageData.selectorList = [
      {
        spmCode: "tag-goods-packing",
        tagId: recommend.data?.recommendPackingGoods?.tagId,
        name: authStore.i18n("cm_home.todaySpecial"),
        goodsList: useChunk(
          recommend.data?.recommendPackingGoods?.goodsList,
          6
        ),
      },
    ];

    // 推荐货盘
    pageData.tagGoodsList = recommend.data?.tagGoodsList;

    //“优质供应商”商品推荐
    pageData.recommendSupplierGoods = {
      spmCode: "homepage-tag-goods",
      tagId: recommend.data?.recommendSupplierGoods?.tagId,
      name: authStore.i18n("cm_home.recommendSupplierGoods"),
      goodsList: useChunk(recommend.data?.recommendSupplierGoods?.goodsList, 6),
    };

    // 客户专属活动;
    recommend.data?.customerExclusiveActivities?.forEach((item: any) => {
      item.goodsList = useChunk(item.goodsList, 6);
    });
    pageData.customerExclusiveActivities =
      recommend.data?.customerExclusiveActivities || [];

    pageData.habitableCapsuleGoods = {
      spmCode: "homepage-tag-goods",
      tagId: recommend.data?.goodsPlugTagMap[HOME_CAPSULE_PRODUCT_ID]?.tagId,
      name: authStore.i18n("cm_home.habitableCapsuleGoods"),
      goodsList: useChunk(
        recommend.data?.goodsPlugTagMap[HOME_CAPSULE_PRODUCT_ID]?.goodsList,
        6
      ),
    };
  }
}

// 推荐商品
async function onRecommendGoods() {
  const res: any = await useRecommendGoods({});
  if (res?.result?.code === 200) {
    pageData.recommendGoods = useChunk(res?.data, 6);
  }
}

// 已登录商品分类
async function onCategoryGoods() {
  if (isEmptyObject(userInfo.value)) return;
  const res: any = await usePcHomePageGoods({});
  if (res?.result?.code === 200) {
    pageData.categoryGoods = res?.data;
  }
}
</script>

<style scoped lang="scss">
.custom-arrow {
  display: flex;
  .custom-arrow-icon {
    width: 30px;
    height: 30px;
    z-index: 10;
    border-radius: 50%;
    margin-right: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #d7d7d7; /* 设置背景颜色为灰色 */
    &:hover {
      background-color: #aaaaaa;
    }
  }
}

.custom-arrow-hot {
  .custom-arrow-icon {
    width: 48px;
    height: 48px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff; /* 设置背景颜色为灰色 */
  }
  .custom-arrow--left {
    left: 0;
  }
  .custom-arrow--right {
    right: 0;
  }
}

.n-card :deep(.n-card__content) {
  padding: 0.25rem 0.25rem;
}
</style>
