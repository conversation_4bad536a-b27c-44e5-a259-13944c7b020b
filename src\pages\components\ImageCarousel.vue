<script setup>
import { ref, onMounted, computed, watch } from "vue";

const props = defineProps({
  images: Array, // 图片数组
  direction: {
    type: String,
    default: "left", // 滚动方向，可选 "left" 或 "right"
  },
  speed: {
    type: Number,
    default: 30, // 滚动速度（秒），数值越小速度越快
  },
  autoplay: {
    type: Boolean,
    default: false, // 默认不自动播放
  },
  gap: {
    type: Number,
    default: 20, // 图片之间的间距
  },
  imageWidth: {
    type: String,
    default: "auto", // 图片宽度，可以是具体像素值或百分比
  },
  imageHeight: {
    type: String,
    default: "238px", // 默认图片高度
  },
});

const container = ref(null);
const scrollContent = ref(null);
const debugInfo = ref([]);

// 拖拽相关状态
const isDragging = ref(false);
const startX = ref(0);
const dragOffset = ref(0);
const animationPaused = ref(false);

// 添加调试日志
const addDebugLog = (message) => {
  const time = new Date().toLocaleTimeString();
  debugInfo.value.unshift(`[${time}] ${message}`);
  if (debugInfo.value.length > 20) {
    debugInfo.value = debugInfo.value.slice(0, 20);
  }
};

// 计算动画持续时间
const animationDuration = computed(() => {
  return `${props.speed}s`;
});

// 计算动画方向
const animationDirection = computed(() => {
  return props.direction === "left" ? "scroll-left" : "scroll-right";
});

// 监听autoplay变化
watch(
  () => props.autoplay,
  (newVal) => {
    addDebugLog(`自动播放状态变更: ${newVal ? "开启" : "关闭"}`);
  }
);

// 监听images变化
watch(
  () => props.images,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      addDebugLog(`图片数量: ${newVal.length}`);
    }
  },
  { immediate: true }
);

onMounted(() => {
  addDebugLog("✅ 组件已挂载，使用成功的CSS动画方案");
  addDebugLog(
    `🎯 配置: 方向=${props.direction}, 速度=${props.speed}s, 间距=${props.gap}px`
  );
  addDebugLog("🔄 动画原理: translateX(0) → translateX(calc(-100% - gap))");
  addDebugLog("📐 布局: 双内容组，min-width: 100%，无缝循环");
});
</script>

<template>
  <div>
    <div ref="container" class="carousel-container">
      <div
        ref="scrollContent"
        class="carousel-content"
        :class="{
          'auto-scroll': autoplay,
          [animationDirection]: autoplay,
        }"
        :style="{
          '--gap': `${gap}px`,
          '--speed': animationDuration,
        }"
      >
        <!-- 第一组图片 -->
        <div class="image-group">
          <img
            v-for="(image, index) in images"
            :key="`group1-${index}`"
            :src="image"
            alt="carousel-image"
            class="carousel-image"
            :style="{ width: imageWidth, height: imageHeight }"
            draggable="false"
            loading="lazy"
          />
        </div>
        <!-- 第二组图片（重复内容，用于无缝循环） -->
        <div class="image-group" aria-hidden="true">
          <img
            v-for="(image, index) in images"
            :key="`group2-${index}`"
            :src="image"
            alt="carousel-image"
            class="carousel-image"
            :style="{ width: imageWidth, height: imageHeight }"
            draggable="false"
            loading="lazy"
          />
        </div>
      </div>
    </div>

    <!-- 调试信息面板 -->
    <div v-if="debugInfo.length > 0" class="debug-panel">
      <div class="debug-header">
        <strong>✅ 无感滚动调试信息 (最新10条)</strong>
        <button @click="debugInfo = []" class="clear-debug">清除</button>
      </div>
      <div class="debug-logs">
        <div
          v-for="(log, index) in debugInfo.slice(0, 10)"
          :key="index"
          class="debug-log"
        >
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.carousel-container {
  overflow: hidden;
  position: relative;
  width: 100%;
  user-select: none;
}

.carousel-content {
  display: flex;
  overflow: hidden;
  user-select: none;
  gap: var(--gap);
}

.image-group {
  flex-shrink: 0;
  display: flex;
  justify-content: space-around;
  min-width: 100%;
  gap: var(--gap);
}

.carousel-image {
  flex-shrink: 0;
  object-fit: cover;
  border-radius: 8px;
}

/* CSS动画实现无感滚动 */
.auto-scroll.scroll-left .image-group {
  animation: scroll-left var(--speed) linear infinite;
}

.auto-scroll.scroll-right .image-group {
  animation: scroll-right var(--speed) linear infinite;
}

@keyframes scroll-left {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(-100% - var(--gap)));
  }
}

@keyframes scroll-right {
  from {
    transform: translateX(calc(-100% - var(--gap)));
  }
  to {
    transform: translateX(0);
  }
}

/* 调试面板样式 */
.debug-panel {
  margin-top: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
  font-family: monospace;
  font-size: 12px;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.clear-debug {
  padding: 4px 8px;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
}

.debug-logs {
  max-height: 200px;
  overflow-y: auto;
}

.debug-log {
  padding: 2px 0;
  border-bottom: 1px solid #ddd;
}

/* 响应式设计 */
@media (prefers-reduced-motion: reduce) {
  .auto-scroll {
    animation: none !important;
  }
}
</style>
