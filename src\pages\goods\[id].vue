<template>
  <div>
    <!-- SEO 部分 -->
    <seo-data :pageData="goodsDetail"></seo-data>
    <page-layout>
      <template #main-content>
        <div>
          <div
            class="bg-white flex min-h-screen"
            v-if="!pageData.showGoodsError"
          >
            <!-- left -->
            <div class="flex-1 mr-4 min-w-[600px]">
              <!-- 商品详情 -->
              <div class="mb-20 mr-10">
                <div class="w-full text-base font-medium mt-4 mb-4">
                  {{ goodsDetail.goodsName }}
                </div>
                <div class="flex items-center mb-2 gap-[12px]">
                  <div>
                    {{ authStore.i18n("cm_goods.goodsNo") }}：{{
                      goodsDetail.goodsNo
                    }}
                  </div>
                  <a
                    :href="`/goods/list/all?padc=${goodsDetail.padc}`"
                    target="_blank"
                    v-if="goodsDetail.paName"
                    class="flex items-center"
                  >
                    <div
                      class="h-[17px] text-[12px] leading-[17px] border border-[#FF4056] rounded-tl-[4px] rounded-bl-[4px] border-r-0 text-[#FF4056] pl-[4px] pr-[2px]"
                    >
                      {{ goodsDetail.paName }}
                    </div>

                    <img
                      src="@/assets/icons/common/tag.svg"
                      :alt="goodsDetail.paName"
                      class="h-[17px]"
                      referrerpolicy="no-referrer"
                    />
                  </a>
                </div>
                <div class="flex w-[520px] h-[520px]">
                  <!-- 副图 -->
                  <image-carousel
                    class="shrink-0 !w-[60px]"
                    :imgSelected="editForm.selectedImage"
                    :listSource="goodsDetail.goodsImageList"
                    :images="goodsDetail.goodsImageList"
                    :showVideoIcon="pageData.showVideoIcon"
                    @selectEvent="onImageSelect"
                  ></image-carousel>
                  <!-- 主图 -->
                  <div class="border-1 border-solid border-gray-20">
                    <video-card
                      v-if="
                        editForm.selectedImage === 0 && goodsDetail.videoUrl
                      "
                      :width="520"
                      :src="goodsDetail.videoUrl"
                      :poster="goodsDetail.coverImage"
                    ></video-card>
                    <n-image
                      v-else
                      lazy
                      preview-disabled
                      object-fit="fill"
                      :style="{
                        padding: '5px',
                        width: '520px',
                        height: '520px',
                        objectFit: 'fill',
                        cursor: 'pointer',
                      }"
                      :src="goodsDetail?.goodsImageList[editForm.selectedImage]"
                      :img-props="{ referrerpolicy: 'no-referrer' }"
                    />
                  </div>
                </div>
              </div>
              <!-- 属性详情 -->
              <div
                class="relative mr-10 break-all"
                v-if="goodsDetail.attrList && goodsDetail.attrList.length"
              >
                <div class="text-xl font-medium mb-5">
                  {{ authStore.i18n("cm_goods.keyAributes") }}
                </div>
                <table class="w-full">
                  <tr
                    v-for="(attr, index) in goodsDetail.attrList"
                    :key="attr.attrId"
                    v-show="index < pageData.showSliceNum"
                  >
                    <td
                      class="border-1 border-solid border-gray-20 p-3 w-2/5 bg-[#F4F4F4]"
                    >
                      {{ attr.attrName }}
                    </td>
                    <td class="border-1 border-solid border-gray-20 p-3 w-3/5">
                      {{ attr.attrValue }}
                    </td>
                  </tr>
                </table>
                <div
                  v-if="
                    goodsDetail.attrList && goodsDetail.attrList.length >= 6
                  "
                  class="show-more"
                  :class="{ less: !pageData.showMore }"
                >
                  <span v-if="pageData.showMore" @click="onShowMore"
                    ><span class="mr-1 hover:underline cursor-pointer">{{
                      authStore.i18n("cm_goods.showMore")
                    }}</span
                    ><icon-card
                      size="30"
                      name="iconamoon:arrow-down-2-light"
                      color="#858585"
                    ></icon-card
                  ></span>
                  <span v-else @click="onShowMore"
                    ><span class="mr-1 hover:underline cursor-pointer">{{
                      authStore.i18n("cm_goods.showLess")
                    }}</span
                    ><icon-card
                      size="30"
                      name="iconamoon:arrow-up-2-light"
                      color="#858585"
                    ></icon-card
                  ></span>
                </div>
              </div>
              <div class="min-w-[600px] mr-10">
                <div class="mt-5">
                  <div class="text-xl font-medium mb-5">
                    {{ authStore.i18n("cm_goods.productDescription") }}
                  </div>

                  <n-scrollbar
                    style="width: 100%; height: auto"
                    x-scrollable
                    class="overflow-y-hidden"
                    ><div v-html="goodsDetail.goodsDesc" class="break-all"></div
                  ></n-scrollbar>
                </div>
              </div>
            </div>
            <!-- right -->
            <div class="w-[470px] border-l" id="right-affix-wrapper">
              <n-affix
                :trigger-top="0"
                class="flex flex-col w-[470px]"
                id="right-affix"
              >
                <n-scrollbar style="height: 100%" x-scrollable>
                  <!-- 商品起批数量和单位 商品价格-->
                  <div class="flex-1 p-4 bg-[#F6F6F6] mb-6">
                    <!-- <div
                      class="text-black mb-2"
                      v-if="
                        goodsDetail?.minBuyQuantity &&
                        goodsDetail?.minBuyQuantity > 1
                      "
                    >
                      {{ authStore.i18n("cm_goods_minBuyQuanity") }}:
                      {{ goodsDetail?.minBuyQuantity }}
                      {{ goodsDetail?.goodsPriceUnitName }}
                    </div> -->
                    <div class="text-[14px] mb-2">
                      {{ authStore.i18n("cm_goods_minRequiredQuantity") }}
                    </div>
                    <div class="flex-1 flex flex-wrap">
                      <div
                        v-for="(
                          stepPrice, index
                        ) in goodsDetail?.goodsPriceRanges"
                        :key="index"
                        class="mr-8"
                      >
                        <!-- 规格单价的最小值和最大值展示；若相同则只展示一个价格值 -->
                        <span
                          class="text-base text-black font-medium mt-2"
                          :class="{
                            '!text-[#DB1925]':
                              editForm.currentStepPriceEnd == stepPrice.end,
                          }"
                        >
                          <span v-if="stepPrice.minPrice != stepPrice.maxPrice"
                            >{{ setUnit(stepPrice.minPrice) }} -
                            {{ stepPrice.maxPrice }}</span
                          >
                          <span v-else>{{ setUnit(stepPrice.maxPrice) }}</span>
                        </span>
                        <div class="text-[#797979] text-xs mt-2">
                          {{ filterPriceRange(stepPrice) }}
                          {{ goodsDetail?.goodsPriceUnitName }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 商品规格 -->
                  <div>
                    <div
                      v-for="(spec, specIndex) in goodsDetail.specList"
                      :key="spec.id"
                      class="ml-3 mt-3"
                    >
                      <div class="font-medium my-1">
                        {{ specIndex + 1 }}. {{ spec.name }}:
                      </div>
                      <div class="flex flex-wrap">
                        <div
                          v-for="item in spec.items"
                          :key="item.itemId"
                          @click="onSpecUpdate(item.itemId, item, specIndex)"
                          class="spec-btn min-w-10 max-w-4/5"
                          :class="{
                            'selected-btn': editForm.selectedSpecList.includes(
                              item.itemId
                            ),
                            'no-stock-btn': item.disabled,
                          }"
                        >
                          <n-popover
                            v-if="item.imageUrl"
                            trigger="hover"
                            placement="left"
                          >
                            <template #trigger>
                              <div class="flex items-center">
                                <n-image
                                  lazy
                                  preview-disabled
                                  object-fit="fill"
                                  class="w-[36px] h-[36px] shrink-0 mr-3"
                                  :src="item.imageUrl"
                                  :img-props="{
                                    referrerpolicy: 'no-referrer',
                                  }"
                                />
                                <span class="py-2">{{ item.itemName }}</span>
                                <div
                                  class="fixed-count"
                                  v-if="editForm.firstSpecTotals[item.itemId]"
                                >
                                  X{{ editForm.firstSpecTotals[item.itemId] }}
                                </div>
                              </div>
                            </template>
                            <n-image
                              lazy
                              preview-disabled
                              object-fit="fill"
                              class="w-[240px] h-[240px]"
                              :src="item.imageUrl"
                              :img-props="{ referrerpolicy: 'no-referrer' }"
                            />
                          </n-popover>
                          <template v-else>
                            <span class="py-2">{{ item.itemName }}</span>
                            <div
                              class="fixed-count"
                              v-if="editForm.firstSpecTotals[item.itemId]"
                            >
                              X{{ editForm.firstSpecTotals[item.itemId] }}
                            </div>
                          </template>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 规格选中列表 -->
                  <div
                    class="rounded-md bg-[#F6F6F6] px-3 py-4 mt-3"
                    v-if="editForm.skuList.length"
                  >
                    <div class="flex justify-between items-center">
                      <!-- <span
                      class="font-medium mb-1"
                      v-if="goodsDetail.minIncreaseQuantity > 1"
                    >
                      <span>{{
                        authStore.i18n("cm_goods.selectedOptions")
                      }}</span
                      ><br />
                      <span>{{
                        `(${authStore.i18n("cm_goods.selectedNum")}:${
                          editForm.totalSpecItem
                        })`
                      }}</span>
                    </span> -->
                      <!-- v-else -->
                      <span class="font-medium mb-1">
                        <span>{{
                          authStore.i18n("cm_goods.selectedOptions")
                        }}</span>
                        <span>{{
                          `(${authStore.i18n("cm_goods.selectedNum")}:${
                            editForm.totalSpecItem
                          })`
                        }}</span>
                      </span>
                      <!-- <span
                      v-if="goodsDetail.minIncreaseQuantity > 1"
                      class="text-black"
                    >
                      {{
                        `${authStore.i18n("cm_goods.minBuyQuanity")} ${
                          goodsDetail.minIncreaseQuantity
                        }`
                      }}
                    </span> -->
                    </div>
                    <table class="sku-table text-black">
                      <thead>
                        <tr>
                          <td
                            style="width: 50%; text-align: left; color: #999999"
                          >
                            {{ authStore.i18n("cm_goods.specifications") }}
                          </td>
                          <td
                            style="width: 20%; text-align: left; color: #999999"
                          >
                            {{ authStore.i18n("cm_goods.specPrice")
                            }}<span class="text-[13px]"
                              >({{ monetaryUnit }})</span
                            >
                          </td>
                          <td
                            style="width: 30%; text-align: left; color: #999999"
                          >
                            {{ authStore.i18n("cm_goods.quantity") }}
                          </td>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          v-for="sku in editForm.skuList"
                          :key="sku.id"
                          :class="{ selected: isSelectedSku(sku) }"
                        >
                          <td style="width: 50%">
                            <n-ellipsis :line-clamp="2" class="w-full">
                              {{ sku.skuName }}</n-ellipsis
                            >
                          </td>
                          <td style="width: 16%">
                            {{ sku.price }}
                          </td>
                          <td style="width: 30%">
                            <n-input-number
                              class="input-number"
                              :min="0"
                              :precision="0"
                              :max="10000000"
                              button-placement="both"
                              v-model:value="sku.cartQty"
                              :step="sku.minIncreaseQuantity"
                              :on-update:value="
                                (value) => onCartQtyUpdata(value, sku)
                              "
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div v-if="editForm.skuList.length" class="pl-4 my-4">
                    <!-- 商品总价 -->
                    <div class="flex items-center w-full font-medium text-base">
                      <div class="w-[52%]">
                        {{ authStore.i18n("cm_goods.totalAmount") }}
                      </div>
                      <div>{{ setUnit(editForm.totalMoneyAmount) }}</div>
                    </div>
                  </div>

                  <!-- 预估运费 -->
                  <div
                    class="w-full border-t-1 border-[#E5E5E5] pl-4 mt-3 pt-3"
                  >
                    <div class="flex items-center mb-[8px]">
                      <div class="text-base font-medium mr-4">
                        {{ authStore.i18n("cm_goods.deliveryFee") }}
                      </div>
                      <country-select
                        @save="onSaveCountry"
                        spm="select_site_from_goods"
                      ></country-select>
                    </div>

                    <div
                      v-if="
                        editForm.routeList?.length && editForm.skuList.length
                      "
                      class="flex flex-col gap-[8px] mt-[8px]"
                    >
                      <div
                        v-for="route in pageData.showExpandedRoutes
                          ? editForm.routeList
                          : editForm.routeList.slice(0, 3)"
                        :key="route.routeId"
                        class="border-1 border-[#D7D7D7] py-[10px] px-[26px] rounded-[10px] flex flex-col gap-[2px] cursor-pointer"
                        :class="{
                          'border-[#E50113]':
                            editForm.selectedRouteId === route.routeId,
                        }"
                        @click="onSelectRoute(route)"
                      >
                        <div class="font-medium">{{ route.routeName }}</div>
                        <div class="flex items-center justify-between">
                          <div>
                            {{ authStore.i18n("cm_goods.shippingCost") }}
                          </div>
                          <div>
                            <span>{{
                              setUnit(route.totalEstimateFreight)
                            }}</span>
                            {{ authStore.i18n("cm_goods.perUnit") }}
                            {{ route.freightGoodsQty }}
                            {{ goodsDetail?.goodsPriceUnitName }}
                          </div>
                        </div>
                        <div class="flex items-center justify-between">
                          <div>
                            {{ authStore.i18n("cm_goods.estimatedTime") }}
                          </div>
                          <div>
                            {{ route?.deliverTimeName }}
                          </div>
                        </div>
                      </div>

                      <!-- 展开收起按钮 -->
                      <div v-if="editForm.routeList.length > 3">
                        <span
                          @click="
                            pageData.showExpandedRoutes =
                              !pageData.showExpandedRoutes
                          "
                          class="inline-flex items-center cursor-pointer"
                        >
                          <span class="mr-1 hover:underline text-[#555]">{{
                            pageData.showExpandedRoutes
                              ? authStore.i18n("cm_goods.showLess")
                              : authStore.i18n("cm_goods.showMore")
                          }}</span>
                          <icon-card
                            size="30"
                            :name="
                              pageData.showExpandedRoutes
                                ? 'iconamoon:arrow-up-2-light'
                                : 'iconamoon:arrow-down-2-light'
                            "
                            color="#858585"
                          ></icon-card>
                        </span>
                      </div>
                    </div>
                    <div v-else-if="goodsDetail?.pcsEstimateFreight">
                      {{ authStore.i18n("cm_goods.shippingCost") }}
                      <span>{{ setUnit(goodsDetail.pcsEstimateFreight) }}</span>
                      /
                      {{ goodsDetail?.goodsPriceUnitName }}
                    </div>
                    <div v-else>
                      {{ authStore.i18n("cm_goods.freightConfirmation") }}
                    </div>
                  </div>
                  <div
                    v-if="
                      editForm.skuList.length &&
                      editForm.selectedRoute?.routeId &&
                      editForm.totalCost
                    "
                    class="w-full pl-4 mt-3 pt-3 flex items-center justify-between font-medium text-base"
                  >
                    <div class="mr-4">
                      {{ authStore.i18n("cm_goods.totalCost") }}
                    </div>
                    <div>
                      {{ setUnit(editForm.totalCost) }} ({{
                        setUnit(editForm.unitPrice)
                      }}/{{ goodsDetail?.goodsPriceUnitName }})
                    </div>
                  </div>

                  <!-- <div class="bg-[rgba(247,186,42,.1)] my-2 px-4 py-1 flex">
                  <icon-card
                    size="22"
                    name="mingcute:warning-line"
                    color="#F7BA2A"
                    class="mr-1"
                  ></icon-card>
                  <span>
                    {{ authStore.i18n("cm_find_offerTip") }}
                  </span>
                </div> -->
                  <!-- <div class="bg-[rgba(10,136,0,.1)] my-2 px-4 py-1 flex">
                  <icon-card
                    size="22"
                    name="mingcute:warning-line"
                    color="#0A8800"
                    class="mr-1"
                  ></icon-card>
                  <span>
                    {{ authStore.i18n("cm_goods_introduce") }}
                  </span>
                </div> -->
                  <div class="my-8" v-if="!pageData.preview">
                    <div class="mb-6 h-8">
                      <div
                        v-show="editForm.errorMessage"
                        class="bg-[#FFF1F1] px-4 py-1 flex"
                      >
                        <icon-card
                          size="22"
                          name="mingcute:warning-line"
                          color="#E52828"
                          class="mr-1"
                        ></icon-card>
                        <span>
                          {{ editForm.errorMessage }}
                        </span>
                      </div>
                    </div>
                    <!-- 询盘提交以及加购 -->
                    <div class="flex items-center">
                      <!-- <n-button
                      :color="
                        editForm.totalMoneyAmount >= 5000 ? '#E50113' : '#fff'
                      "
                      :text-color="
                        editForm.totalMoneyAmount >= 5000 ? '#FFF' : '#333'
                      "
                      class="rounded-[10px] w-[220px] h-[46px] px-0"
                      :class="
                        editForm.totalMoneyAmount < 5000 ? 'border-btn' : ''
                      "
                      @click="onGoFindSubmit($event)"
                    >
                      <div class="flex flex-col">
                        <div>
                          <icon-card
                            name="material-symbols:list-alt-add-outline"
                            size="30"
                            class="mr-1"
                            color="grey"
                          />
                          <span class="text-base">{{
                            authStore.i18n("cm_find.inquireNow")
                          }}</span>
                        </div>
                        <div class="flex justify-center items-center mt-1">
                          <icon-card
                            size="14"
                            name="mingcute:warning-line"
                            class="add-btn-list mr-1"
                            :color="
                              editForm.totalMoneyAmount >= 5000
                                ? '#FFF'
                                : '#333'
                            "
                          ></icon-card>
                          <div class="text-[12px]">
                            {{ authStore.i18n("cm_find_findTip") }}
                          </div>
                        </div>
                      </div>
                    </n-button> -->
                      <n-button
                        color="#E50113"
                        text-color="#fff"
                        @click="onAddCart($event)"
                        data-spm-box="goods-detail-info"
                        class="rounded-[10px] w-[168px] h-[46px] ml-4 button"
                      >
                        <template #icon>
                          <icon-card
                            size="28"
                            name="f7:cart"
                            class="add-btn-list"
                            color="#fff"
                          ></icon-card>
                        </template>
                        <div class="ml-1">
                          {{ authStore.i18n("cm_find_addCart") }}
                        </div>
                      </n-button>
                    </div>
                  </div>
                </n-scrollbar>
              </n-affix>
            </div>
          </div>
          <div
            v-if="pageData.showGoodsError"
            class="w-full flex justify-center items-center min-h-[40vh]"
          >
            <img
              loading="lazy"
              src="@/assets/icons/notFound.svg"
              class="w-[160px] mr-10"
              referrerpolicy="no-referrer"
            />
            <div class="w-[300px]">
              <div class="text-2xl">
                {{ authStore.i18n("cm_goods.notFoundGoods") }}
              </div>
              <n-divider />
              <div v-if="pageData.timeLeft">
                <span
                  class="text-[20px] mr-[0.12rem] text-[#e50113] w-[14px] inline-block"
                  >{{ pageData.timeLeft }}</span
                >{{ authStore.i18n("cm_goods.timeLeftTip") }}
              </div>
              <n-button
                size="large"
                color="#E50113"
                text-color="#fff"
                class="mt-4"
                @click="goHomeClick"
              >
                {{ authStore.i18n("cm_goods.homePage") }}</n-button
              >
            </div>
          </div>
        </div>
      </template>
    </page-layout>

    <n-modal :show="pageData.childDialogVisible">
      <n-card
        style="width: 360px; text-align: center"
        :bordered="false"
        class="add-card"
      >
        <div>
          <icon-card
            size="24"
            name="material-symbols:check-circle"
            class="add-btn-check"
            color="#32CD32"
          ></icon-card>
          {{ authStore.i18n("cm_goods.addToList") }}
        </div>
      </n-card>
    </n-modal>
    <n-modal :show="pageData.errorDialogVisible">
      <n-card
        :bordered="false"
        style="
          width: 420px;
          color: #000;
          padding: 20px 10px !important;
          text-align: center;
        "
      >
        <div>
          <icon-card
            size="24"
            name="mingcute:warning-line"
            class="add-btn-check"
            color="#E50113"
          ></icon-card>
          {{ pageData.errorMessage }}
        </div>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

const nuxtApp = useNuxtApp();
const route = useRoute();
const authStore = useAuthStore();
const goodsDetail = reactive<any>({});
const loginRegister = inject<any>("loginRegister");

const pageData = reactive({
  showSliceNum: 6,
  showMore: true,
  showVideoIcon: false,
  childDialogVisible: false,
  preview: route.query.preview || "",
  errorDialogVisible: false,
  errorMessage: "",
  showGoodsError: false,
  timeLeft: 5,
  showExpandedRoutes: false,
  padc: route.query.padc || "",
});

const editForm = reactive<any>({
  skuList: [],
  selectedSpecList: [],
  firstSpecTotals: {},
  selectedImage: 0,
  errorMessage: null,
  currentStepPriceEnd: null,
  totalSpecItem: null,
  totalMoneyAmount: null,
  freightGoodsQty: null, //参与预估运费的商品数量
  totalEstimateFreight: null, //预估运费总计（全部SKU可预估运费时，才返回）
  selectedRouteId: null,
  selectedRoute: null,
  totalCost: null,
  unitPrice: null,
});

await getGoodsDetail();
const userInfo = computed(() => useAuthStore().getUserInfo);

onMounted(() => {
  authStore.getCartList();
  window.addEventListener("scroll", onScrollX);
  window.addEventListener("resize", onResize);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScrollX);
  window.removeEventListener("resize", onResize);
});

async function getGoodsDetail(type?: any) {
  const res: any = await useGoodsDetail({
    id: route.query.id || route.params.id + "",
    deviceType: 1,
    padc: pageData.padc,
  });
  if (res.result.code === 200) {
    if (type === "updateShippingCost") {
      goodsDetail.pcsEstimateFreight =
        res?.data?.pageData?.pcsEstimateFreight || null;
      return;
    }
    // 移除商品描述含1688链接的a标签的href属性
    if (res?.data?.pageData?.goodsDesc?.includes("detail.1688.com")) {
      res.data.pageData.goodsDesc = res?.data?.pageData?.goodsDesc?.replace(
        /<a\s+([^>]*?)href=["'][^"']*["']([^>]*?)>/gi,
        "<a $1$2>"
      );
      res.data.pageData.goodsDesc = res?.data?.pageData?.goodsDesc?.replace(
        /<area\s+([^>]*?)href=["'][^"']*["']([^>]*?)>/gi,
        "<area $1$2>"
      );
    }
    Object.assign(goodsDetail, res?.data);
    Object.assign(goodsDetail, res?.data?.pageData);
    editForm.selectedRouteId = goodsDetail?.routeId;
    // sku上没有最小加购数量时，则取商品的最小加购数量
    goodsDetail.skuList.forEach((sku: any) => {
      if (!sku.minIncreaseQuantity) {
        sku.minIncreaseQuantity = goodsDetail.minIncreaseQuantity;
      }
    });
    nuxtApp.$setResponseHeaders(goodsDetail.seoData?.responseHeaders);
    goodsDetail.goodsDesc = goodsDetail.goodsDesc.replace(
      /<img\b[^>]*>/gi,
      (match: any) => {
        let updated = match;

        if (!/loading\s*=/.test(updated)) {
          updated = updated.replace("<img", '<img loading="lazy"');
        }

        if (!/referrerpolicy\s*=/.test(updated)) {
          updated = updated.replace(
            "<img",
            '<img referrerpolicy="no-referrer"'
          );
        }

        return updated;
      }
    );

    onInitData();
    pageData.showGoodsError = false;
  } else if (res.result.code === 10000) {
    pageData.showGoodsError = true;
    nextTick(() => {
      pageData.timeLeft = 5;
      const countdown = setInterval(() => {
        if (pageData.timeLeft === 1) {
          navigateToPage("/", {}, false);
          clearInterval(countdown);
        } else {
          pageData.timeLeft--;
        }
      }, 1000);
    });
  }
}

function goHomeClick() {
  navigateToPage("/", {}, false);
}

watch(
  () => editForm.selectedSpecList,
  (newVal: any) => {
    if (
      newVal.length &&
      goodsDetail.specList.length > 1 &&
      newVal.length > goodsDetail.specList.length - 2
    ) {
      goodsDetail.specList[goodsDetail.specList.length - 2].items.forEach(
        (item: any) => {
          if (newVal.includes(item.itemId)) {
            updateSpecStatus();
          }
        }
      );
    }
  },
  { immediate: true, deep: true }
);

watch(
  () => editForm.skuList,
  (newVal: any) => {
    if (newVal.length) {
      editForm.totalSpecItem = newVal.reduce(
        (total: number, existingSku: any) => {
          return total + existingSku.cartQty;
        },
        0
      );
      if (editForm.totalSpecItem < goodsDetail.minBuyQuantity) {
        editForm.errorMessage = `${authStore.i18n("cm_find.minBuyQuantity")} ${
          goodsDetail.minBuyQuantity
        }`;
      } else {
        editForm.errorMessage = null;
      }
      editForm.totalMoneyAmount =
        editForm.skuList.reduce((total: number, existingSku: any) => {
          const subtotal = existingSku.cartQty * existingSku.price * 100;
          return total + subtotal;
        }, 0) / 100;
      onCalculateEstimateFreight();
    } else {
      editForm.totalMoneyAmount = 0;
    }
  },
  { immediate: true, deep: true }
);

// 初始化 回显已选中的sku（默认选择第一个sku进行回显）
function onInitData() {
  // 只有一个规格时 需要在初始化的时候 去判断有没有库存以及上下架
  if (goodsDetail.specList.length == 1) {
    const onlyOneSpecItems = goodsDetail.specList[0].items;
    onlyOneSpecItems.forEach((t: any) => {
      t.disabled = true;
      const matchingSkus = goodsDetail.skuList.filter((sku: any) => {
        return sku.specItems.some(
          (skuSpecItem: any) => skuSpecItem.itemId === t.itemId
        );
      });
      if (matchingSkus.length > 0) {
        matchingSkus.forEach((sku: any) => {
          let disabled = true;
          if (sku.stockQty != 0) {
            disabled = false;
          }
          const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
          onlyOneSpecItems.forEach((item: any) => {
            if (item.itemId == matchingItemId) {
              item.disabled = disabled;
            }
          });
        });
      } else {
        onlyOneSpecItems.forEach((item: any) => {
          item.disabled = true;
        });
      }
    });
  }

  goodsDetail.skuList.forEach((sku: any) => {
    if (sku.cartQty !== 0 && sku.stockQty != 0) {
      sku.skuName = sku.specItems.map((spec: any) => spec.itemName).join(", ");
      // sku的数量须是一次加购量的倍数
      sku.cartQty =
        Math.ceil(sku.cartQty / sku.minIncreaseQuantity) *
        sku.minIncreaseQuantity;
      editForm.skuList.push(sku);
    }
  });
  if (editForm.skuList.length) {
    editForm.skuList[0].specItems.forEach((spec: any, index: any) => {
      if (!editForm.selectedSpecList.includes(spec.itemId)) {
        if (index < editForm.skuList[0].specItems.length - 1) {
          onPushSelectedSpec(spec.itemId);
        }
      }
    });
    const matchingSkus = editForm.skuList.filter((sku: any) => {
      return editForm.selectedSpecList.every((specItemId: string) =>
        sku.specItems.some(
          (skuSpecItem: any) => skuSpecItem.itemId === specItemId
        )
      );
    });
    matchingSkus.forEach((sku: any) => {
      sku.specItems.forEach((spec: any, index: any) => {
        if (index == sku.specItems.length - 1 && sku.specItems.length > 1) {
          onPushSelectedSpec(spec.itemId);
        }
      });
    });
  }
  onUpdateSkuPrice();
  onCountFirstSkuTotal();
  if (goodsDetail.specList && goodsDetail.specList.length) {
    goodsDetail.specList.forEach((spec: any, index: any) => {
      spec.items.forEach((item: any) => {
        if (item.imageUrl) {
          goodsDetail.goodsImageList.push(item.imageUrl);
        }
      });
    });
  }
  if (!!goodsDetail?.videoUrl?.trim()) {
    goodsDetail.goodsImageList?.unshift(goodsDetail?.coverImage);
    pageData.showVideoIcon = true;
  }
}

// 选择销售规格
function onSpecUpdate(itemId: string, item: any, specIndex: any) {
  const remark = `商品编码：${goodsDetail.goodsNo}; 属性名称：${item.itemName}`;
  window?.MyStat?.addPageEvent("click_goods_spec", remark); // 埋点

  // 主图显示sku图片
  if (item.imageUrl) {
    const imgIndex = goodsDetail.goodsImageList.findIndex(
      (url: any) => url == item.imageUrl
    );
    editForm.selectedImage = imgIndex;
  }
  if (item.disabled) return;
  // 更新已选规格列表
  const index = editForm.selectedSpecList.indexOf(itemId);
  if (index == -1) {
    // 如果选择的不是最后一个规格组，则检查当前规格是否已存在同组规格，存在则移除
    if (
      specIndex !== goodsDetail.specList.length - 1 ||
      goodsDetail.specList.length == 1
    ) {
      goodsDetail.specList[specIndex].items.forEach((t: any) => {
        if (editForm.selectedSpecList.includes(t.itemId)) {
          const existingIndex = editForm.selectedSpecList.indexOf(t.itemId);
          if (existingIndex !== -1) {
            editForm.selectedSpecList.splice(existingIndex, 1);
          }
        }
      });
    }
    if (goodsDetail.specList.length > 1) {
      // 重置当前规格组下的所有规格组的选中状态
      for (let i = specIndex + 1; i < goodsDetail.specList.length; i++) {
        goodsDetail.specList[i].items.forEach((it: any, ii: any) => {
          if (editForm.selectedSpecList.includes(it.itemId)) {
            const existingIndex = editForm.selectedSpecList.indexOf(it.itemId);
            if (existingIndex !== -1) {
              editForm.selectedSpecList.splice(existingIndex, 1);
            }
          }
        });
      }
    }

    // 更新当前规格下面所有规格组的禁用状态为初始状态 false
    goodsDetail.specList[goodsDetail.specList.length - 1].items.forEach(
      (t: any) => {
        t.disabled = false;
      }
    );

    // 添加规格
    onPushSelectedSpec(itemId);

    // 判断当前的规格是否存在在已选择的sku数组里 如果有 数据回显
    if (specIndex < goodsDetail.specList.length - 1) {
      // 获取所有满足条件的SKU
      const matchingSkus = editForm.skuList.filter((sku: any) => {
        // 检查当前 SKU 的所有规格项是否都在已选规格列表中，并且库存不为0
        return (
          editForm.selectedSpecList.every((specItemId: string) =>
            sku.specItems.some(
              (skuSpecItem: any) => skuSpecItem.itemId === specItemId
            )
          ) && sku.stockQty !== 0
        );
      });
      if (matchingSkus && matchingSkus.length) {
        matchingSkus[0].specItems.forEach((spec: any, index: any) => {
          if (index < matchingSkus[0].specItems.length - 1) {
            onPushSelectedSpec(spec.itemId);
          }
        });
        matchingSkus.forEach((sku: any) => {
          sku.specItems.forEach((spec: any, index: any) => {
            if (index == sku.specItems.length - 1 && sku.specItems.length > 1) {
              onPushSelectedSpec(spec.itemId);
            }
          });
        });
      }
    }
  }
  // 更新skuList
  onUpdateSkuList(index);
}

// 添加至已选中的规格
function onPushSelectedSpec(itemId: any) {
  if (!editForm.selectedSpecList.includes(itemId)) {
    editForm.selectedSpecList.push(itemId);
  }
}

// 更新最后一组规格组的状态 最后一组规格会校验sku的库存以及下架状态 如果没有查到sku 默认下架
function updateSpecStatus() {
  // 这里需要将选中的最后一组的规格给移除掉 再去找sku
  const selectList = useCloneDeep(editForm.selectedSpecList);
  const lastSpecList = goodsDetail.specList[goodsDetail.specList.length - 1];
  lastSpecList.items.forEach((item: any) => {
    if (selectList.includes(item.itemId)) {
      const existingIndex = selectList.indexOf(item.itemId);
      if (existingIndex !== -1) {
        selectList.splice(existingIndex, 1);
      }
    }
  });
  lastSpecList.items.forEach((t: any) => {
    t.disabled = true;
  });
  // 获取所有满足条件的SKU
  const matchingSkus = goodsDetail.skuList.filter((sku: any) => {
    return selectList.every((specItemId: string) =>
      sku.specItems.some(
        (skuSpecItem: any) => skuSpecItem.itemId === specItemId
      )
    );
  });

  // 如果有满足条件的SKU，则更新规格的可选状态
  if (matchingSkus.length > 0) {
    matchingSkus.forEach((sku: any) => {
      let disabled = true;
      if (sku.stockQty != 0) {
        disabled = false;
      }
      const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
      lastSpecList.items.forEach((item: any) => {
        if (item.itemId == matchingItemId) {
          item.disabled = disabled;
        }
      });
    });
  } else {
    lastSpecList.items.forEach((item: any) => {
      item.disabled = true;
    });
  }
}

// 根据当前选中的规格 高亮选中的sku
function isSelectedSku(sku: any) {
  // 获取当前 SKU 的规格项 ID 数组
  const specItemIds = sku.specItems.map((item: any) => item.itemId);
  // 检查当前 SKU 的所有规格项是否都在选中的列表中
  const allSpecsSelected = specItemIds.every((itemId: any) =>
    editForm.selectedSpecList.includes(itemId)
  );
  // 返回检查结果
  return allSpecsSelected;
}

// 更新选中的skuList
function onUpdateSkuList(index: any) {
  goodsDetail.skuList.forEach((sku: any) => {
    // 检查当前SKU是否满足已选规格条件
    const allSpecsSelected = sku.specItems.every((spec: any) =>
      editForm.selectedSpecList.includes(spec.itemId)
    );
    if (allSpecsSelected) {
      sku.skuName = sku.specItems.map((spec: any) => spec.itemName).join(",");
      // 添加首个sku时 如果起订量大于一次加购量 则取起订量 且是一次加购量的整数倍 否则则取一次加购量
      if (
        editForm.skuList.length == 0 &&
        sku.cartQty === 0 &&
        goodsDetail.minBuyQuantity > sku.minIncreaseQuantity
      ) {
        sku.cartQty =
          Math.ceil(goodsDetail.minBuyQuantity / sku.minIncreaseQuantity) *
          sku.minIncreaseQuantity;
      } else {
        sku.cartQty = sku.cartQty === 0 ? sku.minIncreaseQuantity : sku.cartQty;
      }
      // 检查是否已存在相同的SKU，如果不存在则添加，存在则更新
      const existingIndex = editForm.skuList.findIndex((existingSku: any) =>
        existingSku.specItems.every((spec: any) =>
          sku.specItems.some((newSpec: any) => newSpec.itemId === spec.itemId)
        )
      );

      if (existingIndex == -1) {
        editForm.skuList.push({ ...sku });
      }
    }
  });
  onCountFirstSkuTotal();
  onUpdateSkuPrice();
  if (index !== -1) {
    onUpdateSelectedSpec();
  }
}

// 获取第一规格的sku总和
function onCountFirstSkuTotal() {
  editForm.firstSpecTotals = {};
  // 遍历所有 SKU
  editForm.skuList.forEach((sku: any) => {
    // 获取第一规格的 itemId
    const firstSpecItemId = sku.specItems[0].itemId;
    // 如果该 itemId 在 firstSpecTotals 中不存在，则初始化为 0
    if (!editForm.firstSpecTotals[firstSpecItemId]) {
      editForm.firstSpecTotals[firstSpecItemId] = 0;
    }
    // 将当前 SKU 的数量累加到对应的第一规格数量之和中
    editForm.firstSpecTotals[firstSpecItemId] += sku.cartQty;
  });
}

// 销售规格的加购数修改
function onCartQtyUpdata(value: any, skuInfo: any) {
  if (value) {
    skuInfo.cartQty =
      Math.ceil(value / skuInfo.minIncreaseQuantity) *
      skuInfo.minIncreaseQuantity;
  }

  const skuName = skuInfo.specItems
    .map((spec: any) => spec.itemName)
    .join(", ");
  const remark = `商品编码：${goodsDetail.goodsNo}；属性组合名称：${skuName}；数量：${skuInfo.cartQty}`;
  window?.MyStat?.addPageEvent("click_sku_quantity", remark); // 埋点

  if (value === 0) {
    // 数量减为0 移除 editForm.skuList 中的SKU
    editForm.skuList = editForm.skuList.filter(
      (sku: any) => sku.id !== skuInfo.id
    );
    // 移除当前选中的规格
    const specItemIds = skuInfo.specItems.map((item: any) => item.itemId);
    // 检查当前 SKU 的所有规格项是否都在选中的列表中
    const allSpecsSelected = specItemIds.every((itemId: any) =>
      editForm.selectedSpecList.includes(itemId)
    );
    if (allSpecsSelected) {
      const spe = skuInfo.specItems.find((spec: any, index: any) => {
        if (index == skuInfo.specItems.length - 1) {
          return spec;
        }
      });
      const existingIndex = editForm.selectedSpecList.indexOf(spe.itemId);
      if (existingIndex !== -1) {
        editForm.selectedSpecList.splice(existingIndex, 1);
      }
    }
  }
  onUpdateSkuPrice();
  onUpdateSelectedSpec();
  onCountFirstSkuTotal();
}

// sku数组更新时 更新选中的规格
function onUpdateSelectedSpec() {
  // 移除不再存在于其他 SKU 中的规格
  editForm.selectedSpecList = editForm.selectedSpecList.filter(
    (specId: any) => {
      return editForm.skuList.some((sku: any) => {
        return sku.specItems.some((spec: any) => spec.itemId === specId);
      });
    }
  );
}

// 当前sku总和改变 更新sku的阶梯价格
function onUpdateSkuPrice() {
  const skuTotalQuantity = editForm.skuList.reduce(
    (total: number, sku: any) => {
      return total + sku.cartQty;
    },
    0
  );
  editForm.skuList.forEach((sku: any) => {
    for (const stepPrice of sku.stepPrices) {
      if (skuTotalQuantity <= stepPrice.end || stepPrice.end === -1) {
        sku.price = stepPrice.price;
        editForm.currentStepPriceEnd = stepPrice.end;
        break;
      }
    }
  });
}

// 计算预估运费
async function onCalculateEstimateFreight() {
  const spm = window.MyStat.getLocationParam("spm");
  const result: any = await useCalculateEstimateFreight({
    skuList: editForm.skuList.map((sku: any) => {
      return { skuId: sku.id, quantity: sku.cartQty, spm };
    }),
    siteId: window?.siteData?.siteInfo?.id,
    routeId: editForm.selectedRouteId || null, // 当前页面选中的线路（不传，则取默认值）
  });
  if (result.result?.code === 200) {
    editForm.totalEstimateFreight = result.data?.totalEstimateFreight || null;
    editForm.freightGoodsQty = result.data?.freightGoodsQty || null;
    editForm.routeList = result.data?.routeList || [];
    editForm.selectedRoute =
      editForm.routeList?.find((route: any) => route.selected) || {};
    editForm.selectedRouteId = editForm.selectedRoute?.routeId || null;
    // 计算总计&单价
    onCalculatePriceDetails();
  }
}

function onCalculatePriceDetails() {
  const productCost = Math.round(editForm.totalMoneyAmount * 100);
  const shippingCost = Math.round(
    (editForm.selectedRoute?.totalEstimateFreight || 0) * 100
  );
  const totalCostInCents = productCost + shippingCost;
  editForm.totalCost = Number((totalCostInCents / 100).toFixed(2));
  if (editForm.totalSpecItem > 0) {
    const unitPriceInCents = Math.round(
      (totalCostInCents * 100) / (editForm.totalSpecItem * 100)
    );
    editForm.unitPrice = Number((unitPriceInCents / 100).toFixed(2));
  } else {
    editForm.unitPrice = 0;
  }
}

// 提交前的检查
function onCheckBeforeSubmit() {
  // 未有选中的sku时 点击购物车报错提示的顺序（规格的展示在空间上顺序）
  let missingSpec = null;
  for (let spec of goodsDetail.specList) {
    let itemsSelected = spec.items.some((item: any) =>
      editForm.selectedSpecList.includes(item.itemId)
    );
    if (!itemsSelected) {
      missingSpec = spec.name;
      break;
    }
  }

  if (!editForm.skuList.length) {
    if (missingSpec) {
      showToast(`${authStore.i18n("cm_goods.pleaseSelect")} ${missingSpec}`);
    }
    return false;
  }

  if (editForm.totalSpecItem < goodsDetail.minBuyQuantity) {
    const error = authStore.i18n("cm_goodsDetail.chooseSpecTip");
    editForm.errorMessage = `${error}${goodsDetail.minBuyQuantity})`;
    setTimeout(() => {
      editForm.errorMessage = null;
    }, 3000);
    return false;
  }
  return true;
}

// 加购
async function onAddCart(event: any) {
  const checkPassed = onCheckBeforeSubmit();
  if (!checkPassed) return;

  const spm =
    window.MyStat.getLocationParam("spm") || window.MyStat.getPageSPM(event);
  const skus = editForm.skuList.map((sku: any) => {
    return { skuId: sku.id, quantity: sku.cartQty, spm };
  });
  const params = {
    goodsId: goodsDetail.id,
    skus,
    siteId: window?.siteData?.siteInfo?.id,
    routeId: editForm.selectedRouteId,
    padc: pageData.padc,
  };

  if (!!window?.fbq) {
    window?.fbq("track", "AddToCart", {
      content_ids: [goodsDetail.id],
      content_name: goodsDetail?.goodsName,
      content_type: "product",
      value: setUnit(editForm.totalMoneyAmount),
      currency: "USD",
      contents: skus,
    });
  }
  if (!!window?.ttq) {
    window?.ttq?.track("AddToCart", {
      currency: "USD",
      value: editForm.totalMoneyAmount,
      content_type: "product",
      description: JSON.stringify(skus),
    });
  }
  if (!!window?.gtag) {
    window?.gtag("event", "AddToCart", {
      content_ids: [goodsDetail.id],
      content_name: goodsDetail?.goodsName,
      content_type: "product",
      value: setUnit(editForm.totalMoneyAmount),
      currency: "USD",
      contents: skus,
    });
  }

  // 没有登录 弹出用户登录弹窗 登录成功后再次调用加购接口
  if (isEmptyObject(userInfo.value)) {
    window?.MyStat.addPageEvent(
      "passport_open_add_cart",
      "加入购物车，打开账号窗口"
    ); // 埋点

    loginRegister?.openLogin(params);
    return;
  }

  const result: any = await useAddCart(params);
  if (result.result?.code === 200) {
    pageData.childDialogVisible = true;
    authStore.getCartList();
    setTimeout(() => {
      if (pageData.childDialogVisible) {
        pageData.childDialogVisible = false;
      }
    }, 1500);
  } else if (result?.result?.code === 403) {
    loginRegister?.openLogin();
  } else {
    editForm.errorMessage = result.result?.message;
    setTimeout(() => {
      editForm.errorMessage = null;
    }, 3000);
  }
}

function filterPriceRange(price: any) {
  if (price.end == -1) {
    return `>=${price.start}`;
  } else {
    if (price.start == price.end) {
      return `${price.start}`;
    }
    return `${price.start}-${price.end}`;
  }
}

function onImageSelect(index: number) {
  editForm.selectedImage = index;
}

function onShowMore() {
  pageData.showMore = !pageData.showMore;
  pageData.showSliceNum = pageData.showMore ? 6 : goodsDetail.attrList.length;
}
// async function onGoFindSubmit(e: any) {
//   window?.MyStat?.addPageEvent(
//     "detail_click_inquiry",
//     `商品详情页进入询盘提交页`
//   ); // 埋点

//   const checkPassed = onCheckBeforeSubmit();
//   if (!checkPassed) return;

//   const spm =
//     window.MyStat.getLocationParam("spm") || window.MyStat.getPageSPM(e);
//   const skus = editForm.skuList.map((sku: any) => {
//     return { skuId: sku.id, quantity: sku.cartQty, spm };
//   });
//   if (!!window?.fbq) {
//     window?.fbq("track", "InitiateCheckout", {
//       currency: "USD",
//       num_items: skus?.length,
//       contents: skus,
//     });
//   }
//   if (!!window?.ttq) {
//     window?.ttq?.track("InitiateCheckout", {
//       currency: "USD",
//       value: editForm.totalMoneyAmount,
//       content_type: "product",
//       description: JSON.stringify(skus),
//     });
//   }
//   const res: any = await useGetInquiry({ params: skus });
//   if (res?.result?.code === 200) {
//     const inquiryInfo = res?.data;
//     await authStore.setInquiryInfo(inquiryInfo);
//     window.open(`/find/submit?fromCart=${false}`, "_blank");
//   } else if (res?.result?.code === 403) {
//     loginRegister?.openLogin();
//   } else {
//     pageData.errorDialogVisible = true;
//     setTimeout(() => {
//       pageData.errorDialogVisible = false;
//     }, 3000);
//     pageData.errorMessage =
//       res?.result?.message || authStore.i18n("cm_find.errorMessage");
//   }
// }
// function onGoHome() {
//   window.location.href = "/";
// }

function onResize() {
  onSetRightAffix();
}

// 页面横向滚动时 商品详情右侧部分跟随滚动
function onSetRightAffix() {
  let rightAffix = document.getElementById("right-affix") || <any>{};
  let rightAffixWrapper =
    document.getElementById("right-affix-wrapper") || null;
  if (!rightAffixWrapper) return;
  const documentWidth =
    document.documentElement.clientWidth || document.body.clientWidth;
  const wrapperRight = rightAffixWrapper?.getBoundingClientRect()?.left;
  const wrapperDis = documentWidth - wrapperRight;
  rightAffix.style.right = wrapperDis - rightAffixWrapper.offsetWidth + "px";
}

// 页面横向滚动时 左边内已固定的部分以及右边侧边栏部分跟随滚动
function onScrollX(e: any) {
  onSetRightAffix();

  // 计算滚动到底部信息栏时 右侧固定部分一起向上滚动
  const footerElement = document.getElementById("page-footer");
  const rightAffix = document.getElementById("right-affix");
  if (footerElement && rightAffix) {
    const footerTop =
      footerElement.getBoundingClientRect().top + window.scrollY;
    const scrollTop =
      window.scrollY ||
      document.documentElement.scrollTop ||
      document.body.scrollTop ||
      0;
    const windowHeight =
      window.innerHeight ||
      document.documentElement.clientHeight ||
      document.body.clientHeight;

    // 计算距离底部的距离
    const distanceFromFooter = footerTop - (scrollTop + windowHeight);

    // 判断是否滚动到page-footer元素的位置
    if (distanceFromFooter <= 0) {
      // 计算right-affix的top值
      rightAffix.style.top = distanceFromFooter + "px";
    } else {
      // 如果没有滚动到footer的位置，保持right-affix的初始top值
      rightAffix.style.top = "0px";
    }
  }
}

// 保存选择的国家
const onSaveCountry = () => {
  getGoodsDetail("updateShippingCost");
  if (editForm.skuList.length) {
    onCalculateEstimateFreight();
  }
};

// 选择路线方法
const onSelectRoute = (route: any) => {
  editForm.selectedRoute = route;
  editForm.selectedRouteId = route.routeId;
  onCalculatePriceDetails();
  // 确保只有一条路线被选中
  editForm.routeList.forEach((r: any) => {
    r.selected = r.routeId === route.routeId;
  });
  window?.MyStat?.addPageEvent(
    "select_goods_route",
    `选择了商品（${goodsDetail.goodsNo}）线路：${
      route.routeName
    }，运费：${setUnit(route.partEstimateFreight)} por ${
      route.freightGoodsQty
    } ${goodsDetail.goodsPriceUnitName}`
  );
};
</script>

<style scoped lang="scss">
.wrapper {
  min-height: 100vh;
  display: flex;
  justify-content: flex-start;
  flex-direction: row-reverse;
  width: 100%;
}

.left-wrapper {
  min-width: 1280px;
  flex-shrink: 0;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.container {
  height: auto;
  margin: 0 auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 100vh;
}
.spec-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 2.5rem;
  margin: 0.6rem 1.2rem 0.4rem 0;
  padding: 0 0.5rem;
  min-width: 40px;
  border: 1px solid #d7d7d7;
  cursor: pointer;
  position: relative;
  .fixed-count {
    position: absolute;
    right: -14px;
    top: -10px;
    color: #fff;
    background: #e50113;
    border-radius: 8px;
    padding: 1px 3px;
    font-size: 12px;
  }
}
.selected-btn {
  border: 1px solid #e50113;
}
.no-stock-btn {
  background: #f2f2f2 !important;
  cursor: not-allowed !important;
}

.show-more {
  position: absolute;
  width: 100%;
  line-height: 70px;
  z-index: 1;
  background: #fff;
  bottom: 0;
  left: 0;
  box-shadow: 0px -20px 10px 0px rgba(256, 256, 256, 0.6);
  cursor: pointer;
}
.less {
  box-shadow: none;
  position: relative;
}
.sku-table {
  border-collapse: collapse; /* 合并边框 */
  td {
    padding: 8px;
  }
}
.sku-table tr.selected {
  border: 1px solid #e50113;
}

.border-btn {
  border: 1px solid #b7b7b7;
}

:deep(.input-number .n-input__input-el) {
  text-align: center;
}
:deep(.n-affix) {
  height: 100%;
}

.n-button:hover {
  background: #e50113;
  color: #fff;
  border: none;
  opacity: 0.86;
}
:deep(.n-button__content) {
  width: 100%;
  justify-content: center;
}
:deep(.country-delivery) {
  font-size: 13px;
  margin-right: 10px;
}
:deep(.country-code) {
  font-size: 14px;
}
</style>
