<template>
  <div class="mobile-container bg-[#fff]">
    <seo-data :pageData="goodsDetail"></seo-data>

    <!-- 头部信息 -->
    <mobile-search-box></mobile-search-box>

    <div v-if="!pageData.showGoodsError" class="bg-[#F6F6F6]">
      <!-- 商品图片 -->
      <div class="bg-white">
        <!-- 主图 -->
        <div
          :style="{
            width: pageData.windowWidth + 'px',
            height: pageData.windowWidth + 'px',
            overflow: 'hidden',
          }"
        >
          <video-card
            :width="pageData.windowWidth"
            :height="pageData.windowWidth"
            :src="goodsDetail.videoUrl"
            :poster="goodsDetail.coverImage"
            v-if="pageData.selectedImage === 0 && goodsDetail.videoUrl"
          ></video-card>
          <n-image
            v-else
            lazy
            preview-disabled
            object-fit="fill"
            :style="{
              padding: '0.1rem',
              width: '100%',
              height: '100%',
              objectFit: 'fill',
              cursor: 'pointer',
            }"
            :src="goodsDetail.goodsImageList[pageData.selectedImage]"
            :img-props="{ referrerpolicy: 'no-referrer' }"
          />
        </div>
        <!-- 副图 -->
        <mobile-image-carousel
          class="shrink-0 w-full"
          :imgSelected="pageData.selectedImage"
          :listSource="goodsDetail.goodsImageList"
          :images="goodsDetail.goodsImageList"
          :showVideoIcon="pageData.showVideoIcon"
          @selectEvent="onImageSelect"
        ></mobile-image-carousel>
      </div>
      <!-- 商品详情 -->
      <div class="bg-white rounded-[0.24rem] mt-[0.08rem] p-[0.24rem]">
        <!-- 阶梯价格 -->
        <div class="price-container">
          <div
            class="flex overflow-x-auto whitespace-nowrap scrollable-container"
          >
            <div
              v-for="(stepPrice, index) in goodsDetail?.goodsPriceRanges"
              :key="index"
              class="mr-[0.64rem]"
            >
              <!-- 规格单价的最小值和最大值展示；若相同则只展示一个价格值 -->
              <span
                class="text-[0.32rem] leading-[0.48rem] text-black font-semibold mt-[0.16rem]"
                :class="{
                  '!text-[#DB1925]':
                    pageData.currentStepPriceEnd == stepPrice.end,
                }"
              >
                <span v-if="stepPrice.minPrice != stepPrice.maxPrice"
                  >{{ setUnit(stepPrice.minPrice) }} -
                  {{ stepPrice.maxPrice }}</span
                >
                <span v-else>{{ setUnit(stepPrice.maxPrice) }}</span>
              </span>
              <div
                class="text-[#797979] text-[0.24rem] leading-[0.32rem] mt-[0.16rem]"
              >
                {{ filterPriceRange(stepPrice) }}
                {{ goodsDetail?.goodsPriceUnitName }}
              </div>
            </div>
          </div>
        </div>
        <div
          class="w-full text-[0.28rem] leading-[0.4rem] font-medium my-[0.24rem]"
        >
          {{ goodsDetail.goodsName }}
        </div>
        <div class="text-[0.28rem] leading-[0.28rem]">
          {{ authStore.i18n("cm_goods.goodsNo") }}：{{ goodsDetail.goodsNo }}
        </div>
        <template v-if="goodsDetail.paName">
          <a
            :href="`/h5/search/list?padc=${goodsDetail.padc}`"
            class="inline-flex items-center max-w-full mt-[0.16rem]"
          >
            <span
              class="flex-shrink-0 h-[0.34rem] text-[0.24rem] leading-[0.34rem] border border-[#FF4056] rounded-tl-[0.04rem] rounded-bl-[0.04rem] border-r-0 text-[#FF4056] px-[0.08rem] whitespace-nowrap overflow-hidden text-ellipsis"
            >
              {{ goodsDetail.paName }}
            </span>
            <img
              src="@/assets/icons/common/tag.svg"
              :alt="goodsDetail.paName"
              class="h-[0.34rem] flex-shrink-0"
              referrerpolicy="no-referrer"
            />
          </a>
        </template>
      </div>
      <!-- 商品规格 -->
      <div
        class="bg-white rounded-[0.24rem] mt-[0.08rem] p-[0.24rem] flex items-center justify-between text-[0.28rem] leading-[0.28rem]"
        @click="onOpenDetail"
      >
        <div class="mr-[0.16rem] flex-1 flex items-center">
          <div class="mr-[0.16rem] text-[#7F7F7F]">
            {{ authStore.i18n("cm_goods.specification") }}
          </div>
          <n-ellipsis
            :line-clamp="1"
            class="mr-[0.16rem] break-all"
            :tooltip="false"
          >
            {{ getSpecsName() }}
          </n-ellipsis>
          <div class="shrink-0">{{ authStore.i18n("cm_goods.optional") }}</div>
        </div>
        <icon-card
          size="16"
          color="#7F7F7F"
          class="shrink-0"
          name="ep:arrow-right-bold"
        ></icon-card>
      </div>
      <!-- 商品属性 -->
      <div
        class="bg-white rounded-[0.24rem] mt-[0.08rem] p-[0.24rem]"
        v-if="goodsDetail.attrList && goodsDetail.attrList.length"
      >
        <div
          class="flex items-center justify-between mb-[0.16rem]"
          @click="onOpenAttrDrawer"
        >
          <div class="text-[0.32rem] leading-[0.48rem] font-medium">
            {{ authStore.i18n("cm_goods.keyAributes") }}
          </div>
          <icon-card
            size="16"
            color="#7F7F7F"
            class="shrink-0"
            name="ep:arrow-right-bold"
            v-if="goodsDetail.attrList.length > 5"
          ></icon-card>
        </div>
        <table class="w-full text-[0.28rem] leading-[0.4rem] break-all">
          <tr
            v-for="(attr, index) in goodsDetail.attrList"
            :key="attr.attrId"
            :class="{
              'bg-[#F4F4F4]': index % 2 === 0,
            }"
            v-show="index < 5"
          >
            <td class="p-[0.24rem] w-2/5">
              {{ attr.attrName }}
            </td>
            <td class="p-[0.24rem] w-3/5">
              {{ attr.attrValue }}
            </td>
          </tr>
        </table>
      </div>
      <!-- 商品描述 -->
      <div class="bg-white mt-[0.08rem] p-[0.24rem]">
        <div class="text-[0.32rem] leading-[0.48rem] font-medium mb-[0.16rem]">
          {{ authStore.i18n("cm_goods.productDescription") }}
        </div>
        <div
          v-html="goodsDetail.goodsDesc"
          class="break-all goods-desc w-full"
        ></div>
      </div>

      <!-- 底部 -->
      <div
        class="flex fixed bottom-0 left-0 right-0 items-center bg-[#fff] justify-between px-[0.24rem] py-[0.1rem]"
      >
        <a
          href="/h5/category"
          data-spm-box="goods-detail-category-icon"
          class="flex flex-col justify-center items-center"
        >
          <icon-card size="20" color="grey" name="icon-park-outline:more-app">
          </icon-card>
          <div class="text-[0.24rem] mt-[0.02rem]">
            {{ authStore.i18n("cm_bar.category") }}
          </div>
        </a>
        <div
          class="mobile-cart-btn ml-[0.28rem] mr-[0.6rem] flex flex-col justify-center items-center"
          @click="onFindListClick($event)"
          data-spm-box="goods-detail-cart-icon"
        >
          <icon-card size="24" name="f7:cart" color="grey"></icon-card>
          <div class="text-[0.24rem] mt-[0.02rem]">
            {{ authStore.i18n("cm_bar.list") }}
          </div>
          <div v-if="cartGoodsCount" class="fixed-count">
            {{ cartGoodsCount }}
          </div>
        </div>
        <n-button
          color="#E50113"
          text-color="#fff"
          @click="onOpenDetail($event)"
          class="rounded-[0.2rem] flex-1 h-[0.8rem] px-[0.16rem] text-[0.28rem]"
        >
          <div>
            {{ authStore.i18n("cm_find_addCart") }}
          </div>
        </n-button>
      </div>
    </div>
    <div v-if="pageData.showGoodsError" class="w-full flex justify-center">
      <div class="w-[5.1rem] flex flex-col justify-center items-center">
        <img
          loading="lazy"
          src="@/assets/icons/notFound.svg"
          class="w-[3.2rem] mx-auto mb-[0.2rem]"
          referrerpolicy="no-referrer"
        />
        <div class="text-[0.42rem] leading-[0.52rem]">
          {{ authStore.i18n("cm_goods.notFoundGoods") }}
        </div>
        <n-divider />
        <div v-if="pageData.timeLeft" class="text-[0.28rem]">
          <span
            class="text-[0.4rem] mr-[0.12rem] text-[#e50113] inline-block w-[0.24rem]"
            >{{ pageData.timeLeft }}</span
          >{{ authStore.i18n("cm_goods.timeLeftTip") }}
        </div>
        <n-button
          size="large"
          color="#E50113"
          text-color="#fff"
          class="mt-[0.4rem]"
          @click="goHomeClick"
        >
          {{ authStore.i18n("cm_goods.homePage") }}</n-button
        >
      </div>
    </div>

    <n-drawer
      v-model:show="pageData.showDrawer"
      default-width="100%"
      default-height="75%"
      placement="bottom"
      resizable
    >
      <n-drawer-content closable>
        <template #header>
          <div class="text-[0.32rem] leading-[0.48rem] font-medium">
            {{ authStore.i18n("cm_goods.keyAributes") }}
          </div></template
        >

        <table class="w-full">
          <tr
            v-for="(attr, index) in goodsDetail.attrList"
            :key="attr.attrId"
            :class="{
              'bg-[#F4F4F4]': index % 2 === 0,
            }"
          >
            <td class="p-[0.24rem] w-2/5">
              {{ attr.attrName }}
            </td>
            <td class="p-[0.24rem] w-3/5">
              {{ attr.attrValue }}
            </td>
          </tr>
        </table>
        <template #footer>
          <div
            class="submit-btn cursor-pointer w-full"
            @click="onCloseAttrDrawer"
          >
            {{ authStore.i18n("cm_goods.close") }}
          </div>
        </template>
      </n-drawer-content>
    </n-drawer>

    <goods-spec
      :visible="pageData.visible"
      :goods="goodsDetail"
      @selectEvent="onImageSelect"
      @onCloseDetail="onCloseDetail"
      @onUpdateList="onGetGoodsDetail"
    ></goods-spec>
  </div>
</template>

<script setup lang="ts">
import MobileImageCarousel from "./components/MobileImageCarousel.vue";
import MobileSearchBox from "@/pages/h5/search/components/MobileSearchBox.vue";
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const authStore = useAuthStore();

const goodsDetail = reactive<any>({});
const pageData = reactive({
  selectedImage: 0,
  windowWidth: 400,
  visible: false,
  showDrawer: false,
  showVideoIcon: false,
  currentStepPriceEnd: null,
  showGoodsError: false,
  timeLeft: 5,
});

const userInfo = computed(() => useAuthStore().getUserInfo);

await onGetGoodsDetail();

const cartGoodsCount = computed(() => {
  const count = authStore.$state?.cartStat?.goodsCount;
  return count && count > 99 ? "99+" : count;
});

onMounted(() => {
  authStore.getCartList();
  authStore.setPageHeaderIcon("ep:arrow-left-bold");
  window.addEventListener("resize", updateWindowSize);
  // 初始值
  updateWindowSize();
});

const updateWindowSize = () => {
  pageData.windowWidth = window.innerWidth;
};

function onImageSelect(index: number) {
  pageData.selectedImage = index;
}

async function onGetGoodsDetail() {
  const res: any = await useGoodsDetail({
    id: route.query.id || route.params.id + "",
    deviceType: 1,
    padc: route.query.padc,
  });
  if (res.result.code === 200) {
    // 移除商品描述含1688链接的a标签的href属性
    if (res?.data?.pageData?.goodsDesc?.includes("detail.1688.com")) {
      res.data.pageData.goodsDesc = res?.data?.pageData?.goodsDesc?.replace(
        /<a\s+([^>]*?)href=["'][^"']*["']([^>]*?)>/gi,
        "<a $1$2>"
      );
      res.data.pageData.goodsDesc = res?.data?.pageData?.goodsDesc?.replace(
        /<area\s+([^>]*?)href=["'][^"']*["']([^>]*?)>/gi,
        "<area $1$2>"
      );
    }
    Object.assign(goodsDetail, res?.data);
    Object.assign(goodsDetail, res?.data?.pageData);
    nuxtApp.$setResponseHeaders(goodsDetail.seoData?.responseHeaders);
    goodsDetail.goodsDesc = goodsDetail.goodsDesc.replace(
      /<img\b[^>]*>/gi,
      (match: any) => {
        let updated = match;

        if (!/loading\s*=/.test(updated)) {
          updated = updated.replace("<img", '<img loading="lazy"');
        }

        if (!/referrerpolicy\s*=/.test(updated)) {
          updated = updated.replace(
            "<img",
            '<img referrerpolicy="no-referrer"'
          );
        }

        return updated;
      }
    );
    onInitData();
  } else if (res.result.code === 10000) {
    pageData.showGoodsError = true;
    nextTick(() => {
      pageData.timeLeft = 5;
      const countdown = setInterval(() => {
        if (pageData.timeLeft === 1) {
          navigateToPage("/", {}, false);
          clearInterval(countdown);
        } else {
          pageData.timeLeft--;
        }
      }, 1000);
    });
  }
}

function goHomeClick() {
  navigateToPage("/h5", {}, false);
}

// 初始化
function onInitData() {
  // 主图显示sku图片
  if (goodsDetail.specList && goodsDetail.specList.length) {
    goodsDetail.specList.forEach((spec: any, index: any) => {
      spec.items.forEach((item: any) => {
        if (item.imageUrl) {
          goodsDetail.goodsImageList.push(item.imageUrl);
        }
      });
    });
  }
  if (!!goodsDetail?.videoUrl?.trim()) {
    goodsDetail.goodsImageList?.unshift(goodsDetail?.coverImage);
    pageData.showVideoIcon = true;
  }
}

function filterPriceRange(price: any) {
  if (price.end == -1) {
    return `>=${price.start}`;
  } else {
    if (price.start == price.end) {
      return `${price.start}`;
    }
    return `${price.start}-${price.end}`;
  }
}
function getSpecsName() {
  return goodsDetail.specList.map((spec: any) => spec.name).join("·");
}

function onFindListClick(event: any) {
  // 未登录 去登陆
  if (isEmptyObject(userInfo.value)) {
    navigateToPage("/h5/user/login", { pageSource: "/h5/find" }, false, event);
  } else {
    navigateToPage("/h5/find", {}, false, event);
  }
}

function onOpenAttrDrawer() {
  if (goodsDetail.attrList.length > 5) {
    pageData.showDrawer = true;
  }
}
function onCloseAttrDrawer() {
  pageData.showDrawer = false;
}
function onOpenDetail(e: any) {
  window?.MyStat.addPageEvent(
    "click_goods_dialog",
    `商品编码：${goodsDetail.goodsNo}`
  ); // 埋点

  goodsDetail.spm =
    window.MyStat.getLocationParam("spm") || window.MyStat.getPageSPM(e);
  pageData.visible = true;
}
function onCloseDetail() {
  pageData.visible = false;
}
</script>

<style scoped lang="scss">
.mobile-container {
  min-height: 100vh;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 0.8rem;
}
.scrollable-container {
  overflow-x: auto;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.scrollable-container::-webkit-scrollbar {
  display: none;
}
.price-container {
  position: relative;
}
.price-container::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 0.4rem;
  height: 100%;
  background: #fff;
  z-index: 9;
}

.submit-btn {
  height: 0.8rem;
  line-height: 0.8rem;
  color: #fff;
  background: linear-gradient(270deg, #fc573f, #f12d2a, #e50113);
  text-align: center;
}
:deep(.n-drawer-footer) {
  padding: 0 !important;
}
:deep(.n-drawer-header) {
  padding: 0.2rem 0.2rem !important;
  .n-drawer-header__main {
    margin: 0 auto;
  }
}
:deep(.n-drawer-body-content-wrapper) {
  padding: 0 !important;
}

.n-divider {
  margin: 0.24rem 0;
}
</style>
