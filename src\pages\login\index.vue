<template>
  <div class="page-wrapper">
    <div
      class="w-[1280px] mx-auto flex justify-end pr-[40px] pt-[126px] relative"
    >
      <div class="page-title">
        {{ authStore.i18n("cm_common.inviteNewClients") }}
      </div>
      <img
        loading="lazy"
        src="@/assets/icons/loginPart.png"
        alt="login"
        class="w-[840px] absolute top-[194px] right-[432px]"
      />
      <div
        class="bg-white w-[530px] min-h-[512px] rounded-[8px] px-[80px] py-[32px] flex-shrink-0 relative z-1"
      >
        <div
          class="text-center font-medium text-[30px] leading-[30px] mb-[36px]"
        >
          {{ authStore.i18n("cm_common_login") }}
        </div>
        <div>
          <!-- 登录 -->
          <n-form :rules="rules" ref="loginFromRef" :model="pageData.loginForm">
            <n-form-item
              path="username"
              :label="authStore.i18n('cm_common.username')"
            >
              <n-input
                v-trim
                clearable
                class="h-[52px]"
                @keydown.enter.prevent
                @blur="onBlurEvent(pageData.loginForm.username, 'email')"
                v-model:value="pageData.loginForm.username"
                :placeholder="authStore.i18n('cm_common.inputEmail')"
              >
                <template #prefix>
                  <img
                    loading="lazy"
                    alt="email"
                    class="mr-1.5 w-[22px]"
                    src="@/assets/icons/email.svg"
                  />
                </template>
              </n-input>
            </n-form-item>
            <n-form-item
              path="password"
              :label="authStore.i18n('cm_common.password')"
            >
              <n-input
                v-trim
                clearable
                class="h-[52px]"
                @keydown.enter.prevent
                @blur="onBlurEvent(pageData.loginForm.password, 'password')"
                :type="pageData.showPwd ? 'text' : 'password'"
                v-model:value="pageData.loginForm.password"
                :placeholder="authStore.i18n('cm_common.inputPassword')"
              >
                <template #prefix>
                  <img
                    loading="lazy"
                    alt="lock"
                    class="mr-1.5 w-[22px]"
                    src="@/assets/icons/lock.svg"
                  />
                </template>
                <template #suffix>
                  <icon-card
                    size="24"
                    color="#333"
                    class="cursor-pointer"
                    :name="
                      pageData.showPwd
                        ? 'weui:eyes-on-outlined'
                        : 'weui:eyes-off-outlined'
                    "
                    @click="pageData.showPwd = !pageData.showPwd"
                  /> </template
              ></n-input>
            </n-form-item>
            <div class="flex justify-end mt-[0.2rem] mb-[14px]">
              <div
                @click="onGoModifyPwd"
                class="text-[#e50113] cursor-pointer text-[16px]"
              >
                {{ authStore.i18n("cm_common.forgotMyPwd") }}
              </div>
            </div>
            <n-form-item>
              <n-button
                size="large"
                color="#E50113"
                text-color="#fff"
                class="rounded-[8px] px-[0.16rem] w-full h-[52px] text-[16px] leading-[16px]"
                @click="onLogin(pageData.loginForm)"
              >
                {{ authStore.i18n("cm_common_login") }}
              </n-button>
            </n-form-item>
            <div
              class="flex items-center justify-end text-[16px] leading-[16px] text-[#666]"
            >
              {{ authStore.i18n("cm_common_registerTip") }}
              <div
                class="text-[#e50113] cursor-pointer text-[17px] leading-[17px] ml-[4px]"
                @click="onGoRegister"
              >
                {{ authStore.i18n("cm_common_register") }}
              </div>
            </div>
          </n-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import type { FormInst, FormItemRule, FormRules } from "naive-ui";

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
authStore.setShowAnchor(false);
authStore.setShowCarousel(false);
const loginFromRef = ref<FormInst | null>(null);
const pageData = reactive({
  showPwd: false,
  loginForm: <any>{
    username: "",
    password: "",
  },
});

const rules: FormRules = {
  username: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any) {
      const pattern = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/;
      if (!value) {
        return new Error(authStore.i18n("cm_common.inputEmailTips"));
      } else if (!pattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_email_format_error",
          `邮箱格式错误，邮箱：${value}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.emailTips"));
      }
      return true;
    },
  },
  password: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any) {
      const specialCharPattern = /[^A-Za-z\d]/; // 特殊字符匹配
      const pattern = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$/; // 8-16位包含数字及字母
      if (!value) {
        return new Error(authStore.i18n("cm_common.inputPwdTips"));
      }
      // 校验是否包含特殊字符
      else if (specialCharPattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_password_format_error",
          `密码格式错误：${authStore.i18n("cm_common.pwdFormatTips")}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.pwdFormatTips"));
      }
      // 校验8-16位包含数字及字母
      else if (!pattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_password_format_error",
          `密码格式错误：${authStore.i18n("cm_common.pwdLengthTips")}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.pwdLengthTips"));
      }
      return true;
    },
  },
};

onMounted(() => {
  // 记录未登录首页是否打开过注册登录弹框
  if (route.query.pageSource === "/") {
    window?.MyStat.setSessionValue("isClickLoginModal", "true");
  }
  let loginInfo = sessionStorage.getItem("loginInfo");
  if (loginInfo) {
    const info = JSON.parse(loginInfo);
    pageData.loginForm.username = info.username;
    pageData.loginForm.password = info.password;
    sessionStorage.removeItem("loginInfo");
  }
});

// 去注册
function onGoRegister() {
  const loginInfo = {
    username: pageData.loginForm.username,
    password: pageData.loginForm.password,
  };
  sessionStorage.setItem("loginInfo", JSON.stringify(loginInfo));
  window?.MyStat?.addPageEvent("passport_switch_register", "切换到注册TAB");
  navigateToPage("/register", route.query, false);
}

// 去修改密码
function onGoModifyPwd() {
  const loginInfo = {
    username: pageData.loginForm.username,
    password: pageData.loginForm.password,
  };
  sessionStorage.setItem("loginInfo", JSON.stringify(loginInfo));
  window?.MyStat?.addPageEvent("passport_switch_forgot", "切换到忘记密码TAB");
  router.push({
    name: "modifyPwd",
    query: route.query,
  });
}

// 登录
async function onLogin(params: any) {
  await loginFromRef.value?.validate((errors: any) => {
    errors?.length &&
      window?.MyStat?.addPageEvent(
        "passport_login_verify_fail",
        `登录表单校验不通过：${errors.map((item: any) =>
          item.map((it: any) => it.message)
        )}`
      );
  });
  try {
    const res: any = await useLogin(params);
    if (res?.result?.code === 200) {
      await authStore.setUserInfo(res?.data);
      await onAddCart();
      if (!!window?.gtag) {
        window?.gtag("event", "LoginSuccess");
      }
      window.location.replace(route.query?.pageSource || "/");
    } else {
      showToast(res?.result?.message);
      window?.MyStat?.addPageEvent(
        "passport_login_submit_error",
        `登录提交错误：${res.result?.message}`
      );
    }
  } catch (error) {
    showToast(error);
  }
}

// 加购
async function onAddCart() {
  if (!route.query.goods) return;
  const res: any = await useAddCart(JSON.parse(route.query.goods));
  if (res.result?.code === 200) {
    showToast(
      authStore.i18n("cm_goods.addToList"),
      1500,
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/11/df87d5e1-099d-48ca-97a4-239bb000748a.png"
    );
    authStore.getCartList();
  } else {
    showToast(res.result?.message);
  }
}

function onBlurEvent(val: any, type: string) {
  if (!val) return;
  const eventMap: { [key: string]: { event: string; message: string } } = {
    email: {
      event: "passport_input_email",
      message: "在账号窗口，输入了邮箱",
    },
    password: {
      event: "passport_input_password",
      message: "在账号窗口，输入了密码",
    },
    invite: {
      event: "passport_input_invite",
      message: "在账号窗口，输入了邀请码",
    },
    captcha: {
      event: "passport_input_verify_code",
      message: "在账号窗口，输入了验证码",
    },
  };

  const eventInfo = eventMap[type];
  if (eventInfo) {
    window?.MyStat?.addPageEvent(eventInfo.event, eventInfo.message);
  }
}
</script>

<style scoped lang="scss">
:deep(.n-input__input-el) {
  height: 52px;
}
:deep(.n-form-item-label) {
  font-size: 16px;
  line-height: 16px;
}
.page-wrapper {
  width: 100%;
  height: 100vh;
  min-width: 1280px;
  min-height: 650px;
  background-repeat: no-repeat;
  background-image: url("@/assets/icons/newLoginBg.png");
  background-size: cover;
  overflow: hidden;
}
:deep(.n-input, .n-input--focus) {
  --n-box-shadow-focus: none;
  --n-box-shadow-focus-error: none;
  --n-border-warning: none;
  --n-border-focus-warning: none;
  --n-border-hover-warning: none;
  --n-border: none;
  --n-border-disabled: none;
  --n-border-hover: none;
  --n-border-focus: none;
  background-color: #f2f2f2 !important;
  border-radius: 8px;
}
:deep(.n-input--focus) {
  background-color: #f2f2f2;
}
:deep(.n-input__placeholder) {
  font-size: 16px;
  line-height: 16px;
}
.page-title {
  width: 540px;
  position: relative;
  text-align: center;
  z-index: 1;
  margin-right: 64px;
  margin-top: 10px;
  font-size: 48px;
  font-style: italic;
  font-weight: 600;
  line-height: 48px;
  text-shadow: 2px 4px 8px rgba(229, 1, 19, 0.9);
  color: #fff4d5;
}
</style>
