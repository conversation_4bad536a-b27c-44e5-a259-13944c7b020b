<template>
  <n-drawer
    resizable
    default-width="100%"
    default-height="90%"
    placement="bottom"
    :on-after-leave="onCloseDetail"
    v-model:show="pageData.dialogVisible"
  >
    <n-drawer-content
      closable
      :class="!!editForm.errorMessage ? 'error-drawer' : 'noraml-drawer'"
    >
      <template #header>
        <div class="text-[0.32rem] leading-[0.48rem] font-semibold">
          {{ authStore.i18n("cm_goods.specifications") }}
        </div></template
      >
      <div class="w-full">
        <!-- 商品起批数量和单位 商品价格-->
        <div class="price-container mb-[0.24rem]">
          <!-- <div
            class="text-[0.24rem] leading-[0.32rem] text-black mb-[0.16rem]"
            v-if="
              goodsDetail?.minBuyQuantity && goodsDetail?.minBuyQuantity > 1
            "
          >
            {{ authStore.i18n("cm_goods_minBuyQuanity") }}:
            {{ goodsDetail?.minBuyQuantity }}
            {{ goodsDetail?.goodsPriceUnitName }}
          </div> -->
          <div class="text-[0.26rem] mb-[0.16rem]">
            {{ authStore.i18n("cm_goods_minRequiredQuantity") }}
          </div>
          <div
            class="flex overflow-x-auto whitespace-nowrap scrollable-container"
          >
            <div
              v-for="(stepPrice, index) in goodsDetail?.goodsPriceRanges"
              :key="index"
              class="mr-[0.64rem]"
            >
              <!-- 规格单价的最小值和最大值展示；若相同则只展示一个价格值 -->
              <span
                class="text-[0.32rem] leading-[0.48rem] text-black font-medium mt-[0.16rem]"
                :class="{
                  '!text-[#DB1925]':
                    editForm.currentStepPriceEnd == stepPrice.end,
                }"
              >
                <span v-if="stepPrice.minPrice != stepPrice.maxPrice"
                  >{{ setUnit(stepPrice.minPrice) }} -
                  {{ stepPrice.maxPrice }}</span
                >
                <span v-else>{{ setUnit(stepPrice.maxPrice) }}</span>
              </span>
              <div
                class="text-[#797979] text-[0.24rem] leading-[0.32rem] mt-[0.16rem]"
              >
                {{ filterPriceRange(stepPrice) }}
                {{ goodsDetail?.goodsPriceUnitName }}
              </div>
            </div>
          </div>
        </div>
        <!-- 商品规格 -->
        <div>
          <div
            v-for="(spec, specIndex) in goodsDetail.specList"
            :key="spec.id"
            class="ml-[0.16rem] mt-[0.24rem]"
          >
            <div class="font-medium text-[0.28rem]">
              {{ specIndex + 1 }}. {{ spec.name }}:
            </div>
            <div class="flex flex-wrap">
              <div
                v-for="item in spec.items"
                :key="item.itemId"
                @click="onSpecUpdate(item.itemId, item, specIndex)"
                class="spec-btn min-w-[0.8rem] max-w-4/5"
                :class="{
                  'selected-btn': editForm.selectedSpecList.includes(
                    item.itemId
                  ),
                  'no-stock-btn': item.disabled,
                }"
              >
                <n-image
                  lazy
                  preview-disabled
                  object-fit="fill"
                  class="w-[36px] h-[36px] shrink-0 mr-[0.24rem]"
                  :src="item.imageUrl"
                  v-if="item.imageUrl"
                  :img-props="{ referrerpolicy: 'no-referrer' }"
                />
                <span class="py-[0.16rem] text-[0.28rem]">{{
                  item.itemName
                }}</span>
                <div
                  class="fixed-count"
                  v-if="editForm.firstSpecTotals[item.itemId]"
                >
                  X{{ editForm.firstSpecTotals[item.itemId] }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 规格选中列表 -->
        <div
          class="rounded-md bg-[#F6F6F6] px-[0.16rem] py-[0.24rem] my-[0.24rem]"
          v-if="editForm.skuList.length"
        >
          <div class="flex justify-between items-center text-[0.28rem]">
            <!-- <span
              class="font-medium mb-[0.08rem]"
              v-if="goodsDetail.minIncreaseQuantity > 1"
            >
              <span>{{ authStore.i18n("cm_goods.selectedOptions") }}</span
              ><br />
              <span>{{
                `(${authStore.i18n("cm_goods.selectedNum")}:${
                  editForm.totalSpecItem
                })`
              }}</span>
            </span> -->
            <!-- v-else -->
            <span class="font-medium mb-[0.08rem]">
              <span>{{ authStore.i18n("cm_goods.selectedOptions") }}</span>
              <span>{{
                `(${authStore.i18n("cm_goods.selectedNum")}:${
                  editForm.totalSpecItem
                })`
              }}</span>
            </span>
            <!-- <span v-if="goodsDetail.minIncreaseQuantity > 1" class="text-black">
              {{
                `${authStore.i18n("cm_goods.minBuyQuanity")} ${
                  goodsDetail.minIncreaseQuantity
                }`
              }}
            </span> -->
          </div>
          <table class="sku-table text-black">
            <thead>
              <tr>
                <td style="width: 42%; text-align: left; color: #999999">
                  {{ authStore.i18n("cm_goods.specifications") }}
                </td>
                <td style="width: 25%; text-align: left; color: #999999">
                  {{ authStore.i18n("cm_goods.specPrice") }}
                  <div class="text-[0.24rem]">({{ monetaryUnit }})</div>
                </td>
                <td style="width: 30%; text-align: left; color: #999999">
                  {{ authStore.i18n("cm_goods.quantity") }}
                </td>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="sku in editForm.skuList"
                :key="sku.id"
                :class="{ selected: isSelectedSku(sku) }"
              >
                <td style="width: 42%">
                  <n-ellipsis :line-clamp="2" class="w-full">
                    {{ sku.skuName }}</n-ellipsis
                  >
                </td>
                <td style="width: 25%">
                  {{ sku.price }}
                </td>
                <td style="width: 30%">
                  <n-input-number
                    class="input-number"
                    :min="0"
                    :precision="0"
                    :max="10000000"
                    button-placement="both"
                    v-model:value="sku.cartQty"
                    :step="sku.minIncreaseQuantity"
                    :on-update:value="(value) => onCartQtyUpdata(value, sku)"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-if="editForm.skuList.length" class="px-[0.28rem] my-[0.32rem]">
          <!-- 商品总价 -->
          <div class="flex items-center w-full font-medium text-[0.3rem]">
            <div class="w-[44%]">
              {{ authStore.i18n("cm_goods.totalAmount") }}
            </div>
            <div>{{ setUnit(editForm.totalMoneyAmount) }}</div>
          </div>
        </div>
        <!-- 预估运费 -->
        <div
          class="border-t-1 border-[#E5E5E5] px-[0.28rem] mt-[0.2rem] mb-[0.32rem] pt-[0.2rem] text-[0.28rem]"
        >
          <div class="flex mb-[0.16rem]">
            <div class="text-[0.32rem] font-medium mr-[0.24rem]">
              {{ authStore.i18n("cm_goods.deliveryFee") }}
            </div>
            <mobile-country-select
              @save="onSaveCountry"
              spm="select_site_from_goods"
            />
          </div>
          <div
            v-if="editForm.routeList?.length && editForm.skuList.length"
            class="flex flex-col gap-[8px] mt-[0.2rem]"
          >
            <div
              v-for="route in pageData.showExpandedRoutes
                ? editForm.routeList
                : editForm.routeList.slice(0, 3)"
              :key="route.routeId"
              class="border-1 border-[#D7D7D7] py-[0.2rem] px-[0.24rem] rounded-[0.2rem] flex flex-col gap-[0.04rem] cursor-pointer"
              :class="{
                'border-[#E50113]': editForm.selectedRouteId === route.routeId,
              }"
              @click="onSelectRoute(route)"
            >
              <div class="font-medium">{{ route.routeName }}</div>
              <div class="flex items-center justify-between">
                <div>{{ authStore.i18n("cm_goods.shippingCost") }}</div>
                <div>
                  <span>{{ setUnit(route.totalEstimateFreight) }}</span>
                  {{ authStore.i18n("cm_goods.perUnit") }}
                  {{ route.freightGoodsQty }}
                  {{ goodsDetail?.goodsPriceUnitName }}
                </div>
              </div>
              <div class="flex items-center justify-between">
                <div>
                  {{ authStore.i18n("cm_goods.estimatedTime") }}
                </div>
                <div>
                  {{ route?.deliverTimeName }}
                </div>
              </div>
            </div>

            <!-- 展开收起按钮 -->
            <div v-if="editForm.routeList.length > 3">
              <span
                @click="
                  pageData.showExpandedRoutes = !pageData.showExpandedRoutes
                "
                class="inline-flex items-center cursor-pointer"
              >
                <span class="mr-[0.08rem] hover:underline text-[#555]">{{
                  pageData.showExpandedRoutes
                    ? authStore.i18n("cm_goods.showLess")
                    : authStore.i18n("cm_goods.showMore")
                }}</span>
                <icon-card
                  size="26"
                  :name="
                    pageData.showExpandedRoutes
                      ? 'iconamoon:arrow-up-2-light'
                      : 'iconamoon:arrow-down-2-light'
                  "
                  color="#858585"
                ></icon-card>
              </span>
            </div>
          </div>
          <div v-else-if="goodsDetail?.pcsEstimateFreight">
            {{ authStore.i18n("cm_goods.shippingCost") }}
            <span>{{ setUnit(goodsDetail.pcsEstimateFreight) }}</span>
            /
            {{ goodsDetail?.goodsPriceUnitName }}
          </div>
          <div v-else>
            {{ authStore.i18n("cm_goods.freightConfirmation") }}
          </div>
        </div>
        <div
          v-if="
            editForm.skuList.length &&
            editForm.selectedRoute?.routeId &&
            editForm.totalCost
          "
          class="w-full pt-[0.1rem] pb-[0.3rem] px-[0.24rem] rounded-[0.2rem] flex items-center justify-between font-medium text-[0.32rem]"
        >
          <div>
            {{ authStore.i18n("cm_goods.totalCost") }}
          </div>
          <div class="text-[0.3rem]">
            {{ setUnit(editForm.totalCost) }} ({{
              setUnit(editForm.unitPrice)
            }}/{{ goodsDetail?.goodsPriceUnitName }})
          </div>
        </div>
        <!-- <div
          class="bg-[rgba(247,186,42,.1)] my-[0.18rem] px-[0.2rem] py-[0.08rem] flex text-[0.26rem]"
        >
          <icon-card
            size="22"
            name="mingcute:warning-line"
            color="#F7BA2A"
            class="mr-[0.08rem]"
          ></icon-card>
          <span>
            {{ authStore.i18n("cm_find_offerTip") }}
          </span>
        </div> -->
        <!-- <div
          class="bg-[rgba(10,136,0,.1)] my-[0.18rem] px-[0.2rem] py-[0.08rem] flex text-[0.26rem]"
        >
          <icon-card
            size="22"
            name="mingcute:warning-line"
            color="#0A8800"
            class="mr-[0.08rem]"
          ></icon-card>
          <span>
            {{ authStore.i18n("cm_goods_introduce") }}
          </span>
        </div> -->
      </div>
      <template #footer>
        <div
          v-show="editForm.errorMessage"
          class="bg-[#FFF1F1] px-[0.2rem] py-[0.08rem] flex items-center absolute bottom-[0.92rem] right-0 left-0"
        >
          <icon-card
            size="24"
            name="mingcute:warning-line"
            color="#E52828"
            class="mr-[0.16rem]"
          ></icon-card>
          <span>
            {{ editForm.errorMessage }}
          </span>
        </div>
        <div
          class="flex justify-between items-center overflow-hidden mx-auto w-full px-[0.24rem]"
        >
          <!-- <n-button
            :color="editForm.totalMoneyAmount >= 5000 ? '#E50113' : '#fff'"
            :text-color="editForm.totalMoneyAmount >= 5000 ? '#FFF' : '#333'"
            class="rounded-[0.2rem] w-[54%] h-[0.92rem] px-[0.08rem] mr-[0.08rem]"
            :class="editForm.totalMoneyAmount < 5000 ? 'border-btn' : ''"
            @click="onGoFindSubmit"
          >
            <div class="flex flex-col">
              <div>
                <icon-card
                  name="material-symbols:list-alt-add-outline"
                  size="28"
                  class="mr-[0.08rem]"
                  color="grey"
                />
                <span class="text-[0.3rem] leading-[0.48rem]">{{
                  authStore.i18n("cm_find.inquireNow")
                }}</span>
              </div>
              <div class="flex justify-center items-center mt-[0.08rem]">
                <icon-card
                  size="14"
                  name="mingcute:warning-line"
                  class="add-btn-list mr-[0.08rem]"
                  :color="editForm.totalMoneyAmount >= 5000 ? '#FFF' : '#333'"
                ></icon-card>
                <div class="text-[0.24rem]">
                  {{ authStore.i18n("cm_find_findTip") }}
                </div>
              </div>
            </div>
          </n-button> -->
          <n-button
            color="#E50113"
            text-color="#fff"
            @click="onAddCart"
            class="rounded-[0.2rem] w-[89%] h-[0.92rem] px-[0.16rem] text-[0.28rem]"
          >
            <template #icon>
              <icon-card
                size="26"
                name="f7:cart"
                class="add-btn-list mr-[0.2rem]"
                color="#fff"
              ></icon-card>
            </template>
            <div class="w-full whitespace-normal">
              {{ authStore.i18n("cm_find_addCartConfirm") }}
            </div>
          </n-button>
          <div
            class="mobile-cart-btn"
            @click="onFindListClick($event)"
            data-spm-box="goods-detail-cart-icon"
          >
            <icon-card size="0.56rem" name="f7:cart" color="grey"></icon-card>
            <div v-if="cartGoodsCount" class="fixed-count">
              {{ cartGoodsCount }}
            </div>
          </div>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts" name="GoodsSpec">
import { useAuthStore } from "@/stores/authStore";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  goods: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(["selectEvent", "onCloseDetail", "onUpdateList"]);

const userInfo = computed(() => useAuthStore().getUserInfo);
const cartGoodsCount = computed(() => {
  const count = authStore.$state?.cartStat?.goodsCount;
  return count && count > 99 ? "99+" : count;
});

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const goodsDetail = reactive<any>({});
const editForm = reactive<any>({
  skuList: [],
  selectedSpecList: [],
  firstSpecTotals: {},
  errorMessage: null,
  currentStepPriceEnd: null,
  totalSpecItem: null,
  totalMoneyAmount: null,
  freightGoodsQty: null, //参与预估运费的商品数量
  totalEstimateFreight: null, //预估运费总计（全部SKU可预估运费时，才返回）
  selectedRoute: null,
  totalCost: null,
  unitPrice: null,
});

const pageData = reactive(<any>{
  dialogVisible: props.visible,
  showExpandedRoutes: false,
});

watch(
  () => props.visible,
  (newVal: any) => {
    if (newVal) {
      goodsDetail.boxEstimateFreight = null;
      goodsDetail.boxFreightGoodsQty = null;
      editForm.skuList = [];
      editForm.selectedSpecList = [];
      editForm.firstSpecTotals = {};
      editForm.errorMessage = null;
      editForm.currentStepPriceEnd = null;
      editForm.totalMoneyAmount = null;
      editForm.totalSpecItem = null;
      editForm.freightGoodsQty = null;
      editForm.totalEstimateFreight = null;
      editForm.selectedRouteId = null;
      editForm.selectedRoute = null;
      editForm.totalCost = null;
      editForm.unitPrice = null;
      onGetGoodsDetail();
    }
    pageData.dialogVisible = newVal;
  },
  { deep: true }
);

watch(
  () => editForm.selectedSpecList,
  (newVal: any) => {
    if (
      newVal.length &&
      goodsDetail.specList.length > 1 &&
      newVal.length > goodsDetail.specList.length - 2
    ) {
      goodsDetail.specList[goodsDetail.specList.length - 2].items.forEach(
        (item: any) => {
          if (newVal.includes(item.itemId)) {
            updateSpecStatus();
          }
        }
      );
    }
  },
  { immediate: true, deep: true }
);

watch(
  () => editForm.skuList,
  (newVal: any) => {
    if (newVal.length) {
      editForm.totalSpecItem = newVal.reduce(
        (total: number, existingSku: any) => {
          return total + existingSku.cartQty;
        },
        0
      );
      if (editForm.totalSpecItem < goodsDetail.minBuyQuantity) {
        editForm.errorMessage = `${authStore.i18n("cm_find.minBuyQuantity")} ${
          goodsDetail.minBuyQuantity
        }`;
      } else {
        editForm.errorMessage = null;
      }
      editForm.totalMoneyAmount =
        editForm.skuList.reduce((total: number, existingSku: any) => {
          const subtotal = existingSku.cartQty * existingSku.price * 100; // 将金额转换为整数（以分为单位）
          return total + subtotal;
        }, 0) / 100;
      onCalculateEstimateFreight();
    } else {
      editForm.totalMoneyAmount = 0;
    }
  },
  { immediate: true, deep: true }
);

onMounted(() => {});

async function onGetGoodsDetail(type?: any) {
  const res: any = await useGoodsInfo({
    id: props.goods.goodsId || props.goods.id,
    deviceType: 1,
    padc: props.goods.padc,
  });
  if (res.result.code === 200) {
    if (type === "updateShippingCost") {
      goodsDetail.pcsEstimateFreight = res?.data?.pcsEstimateFreight || null;
      return;
    }
    Object.assign(goodsDetail, res?.data);
    goodsDetail.pcsEstimateFreight = res?.data?.pcsEstimateFreight || null;
    editForm.selectedRouteId = goodsDetail?.routeId;
    // sku上没有最小加购数量时，则取商品的最小加购数量
    goodsDetail.skuList.forEach((sku: any) => {
      if (!sku.minIncreaseQuantity) {
        sku.minIncreaseQuantity = goodsDetail.minIncreaseQuantity;
      }
    });
    onInitData();
    window?.MyStat?.addPageEvent(
      "click_goods_dialog",
      `商品编码：${goodsDetail.goodsNo}`
    ); // 埋点
  }
}

// 初始化 回显已选中的sku（默认选择第一个sku进行回显）
function onInitData() {
  // 只有一个规格时 需要在初始化的时候 去判断有没有库存以及上下架
  if (goodsDetail.specList.length == 1) {
    const onlyOneSpecItems = goodsDetail.specList[0].items;
    onlyOneSpecItems.forEach((t: any) => {
      t.disabled = true;
      const matchingSkus = goodsDetail.skuList.filter((sku: any) => {
        return sku.specItems.some(
          (skuSpecItem: any) => skuSpecItem.itemId === t.itemId
        );
      });
      if (matchingSkus.length > 0) {
        matchingSkus.forEach((sku: any) => {
          let disabled = true;
          if (sku.stockQty != 0) {
            disabled = false;
          }
          const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
          onlyOneSpecItems.forEach((item: any) => {
            if (item.itemId == matchingItemId) {
              item.disabled = disabled;
            }
          });
        });
      } else {
        onlyOneSpecItems.forEach((item: any) => {
          item.disabled = true;
        });
      }
    });
  }

  goodsDetail.skuList.forEach((sku: any) => {
    if (sku.cartQty !== 0 && sku.stockQty != 0) {
      sku.skuName = sku.specItems.map((spec: any) => spec.itemName).join(", ");
      // sku的数量须是一次加购量的倍数
      sku.cartQty =
        Math.ceil(sku.cartQty / sku.minIncreaseQuantity) *
        sku.minIncreaseQuantity;
      editForm.skuList.push(sku);
    }
  });
  if (editForm.skuList.length) {
    editForm.skuList[0].specItems.forEach((spec: any, index: any) => {
      if (!editForm.selectedSpecList.includes(spec.itemId)) {
        if (index < editForm.skuList[0].specItems.length - 1) {
          onPushSelectedSpec(spec.itemId);
        }
      }
    });
    const matchingSkus = editForm.skuList.filter((sku: any) => {
      return editForm.selectedSpecList.every((specItemId: string) =>
        sku.specItems.some(
          (skuSpecItem: any) => skuSpecItem.itemId === specItemId
        )
      );
    });
    matchingSkus.forEach((sku: any) => {
      sku.specItems.forEach((spec: any, index: any) => {
        if (index == sku.specItems.length - 1 && sku.specItems.length > 1) {
          onPushSelectedSpec(spec.itemId);
        }
      });
    });
  }
  onUpdateSkuPrice();
  onCountFirstSkuTotal();

  if (goodsDetail.specList && goodsDetail.specList.length) {
    goodsDetail.specList.forEach((spec: any, index: any) => {
      spec.items.forEach((item: any) => {
        if (item.imageUrl) {
          goodsDetail.goodsImageList.push(item.imageUrl);
        }
      });
    });
  }
  if (!!goodsDetail?.videoUrl?.trim()) {
    goodsDetail.goodsImageList?.unshift(goodsDetail?.coverImage);
    pageData.showVideoIcon = true;
  }
}

// 选择销售规格
function onSpecUpdate(itemId: string, item: any, specIndex: any) {
  const remark = `商品编码：${goodsDetail.goodsNo}; 属性名称：${item.itemName}`;
  window?.MyStat?.addPageEvent("click_goods_spec", remark); // 埋点

  // 主图显示sku图片
  if (item.imageUrl) {
    const imgIndex = goodsDetail.goodsImageList.findIndex(
      (url: any) => url == item.imageUrl
    );
    emit("selectEvent", imgIndex);
  }
  if (item.disabled) return;
  // 更新已选规格列表
  const index = editForm.selectedSpecList.indexOf(itemId);
  if (index == -1) {
    // 如果选择的不是最后一个规格组，则检查当前规格是否已存在同组规格，存在则移除
    if (
      specIndex !== goodsDetail.specList.length - 1 ||
      goodsDetail.specList.length == 1
    ) {
      goodsDetail.specList[specIndex].items.forEach((t: any) => {
        if (editForm.selectedSpecList.includes(t.itemId)) {
          const existingIndex = editForm.selectedSpecList.indexOf(t.itemId);
          if (existingIndex !== -1) {
            editForm.selectedSpecList.splice(existingIndex, 1);
          }
        }
      });
    }
    if (goodsDetail.specList.length > 1) {
      // 重置当前规格组下的所有规格组的选中状态
      for (let i = specIndex + 1; i < goodsDetail.specList.length; i++) {
        goodsDetail.specList[i].items.forEach((it: any, ii: any) => {
          if (editForm.selectedSpecList.includes(it.itemId)) {
            const existingIndex = editForm.selectedSpecList.indexOf(it.itemId);
            if (existingIndex !== -1) {
              editForm.selectedSpecList.splice(existingIndex, 1);
            }
          }
        });
      }
    }

    // 更新当前规格下面所有规格组的禁用状态为初始状态 false
    goodsDetail.specList[goodsDetail.specList.length - 1].items.forEach(
      (t: any) => {
        t.disabled = false;
      }
    );

    // 添加规格
    onPushSelectedSpec(itemId);

    // 判断当前的规格是否存在在已选择的sku数组里 如果有 数据回显
    if (specIndex < goodsDetail.specList.length - 1) {
      // 获取所有满足条件的SKU
      const matchingSkus = editForm.skuList.filter((sku: any) => {
        // 检查当前 SKU 的所有规格项是否都在已选规格列表中，并且库存不为0
        return (
          editForm.selectedSpecList.every((specItemId: string) =>
            sku.specItems.some(
              (skuSpecItem: any) => skuSpecItem.itemId === specItemId
            )
          ) && sku.stockQty !== 0
        );
      });
      if (matchingSkus && matchingSkus.length) {
        matchingSkus[0].specItems.forEach((spec: any, index: any) => {
          if (index < matchingSkus[0].specItems.length - 1) {
            onPushSelectedSpec(spec.itemId);
          }
        });
        matchingSkus.forEach((sku: any) => {
          sku.specItems.forEach((spec: any, index: any) => {
            if (index == sku.specItems.length - 1 && sku.specItems.length > 1) {
              onPushSelectedSpec(spec.itemId);
            }
          });
        });
      }
    }
  }
  // 更新skuList
  onUpdateSkuList(index);
}

// 添加至已选中的规格
function onPushSelectedSpec(itemId: any) {
  if (!editForm.selectedSpecList.includes(itemId)) {
    editForm.selectedSpecList.push(itemId);
  }
}

// 更新最后一组规格组的状态 最后一组规格会校验sku的库存以及下架状态 如果没有查到sku 默认下架
function updateSpecStatus() {
  // 这里需要将选中的最后一组的规格给移除掉 再去找sku
  const selectList = useCloneDeep(editForm.selectedSpecList);
  const lastSpecList = goodsDetail.specList[goodsDetail.specList.length - 1];
  lastSpecList.items.forEach((item: any) => {
    if (selectList.includes(item.itemId)) {
      const existingIndex = selectList.indexOf(item.itemId);
      if (existingIndex !== -1) {
        selectList.splice(existingIndex, 1);
      }
    }
  });
  lastSpecList.items.forEach((t: any) => {
    t.disabled = true;
  });
  // 获取所有满足条件的SKU
  const matchingSkus = goodsDetail.skuList.filter((sku: any) => {
    return selectList.every((specItemId: string) =>
      sku.specItems.some(
        (skuSpecItem: any) => skuSpecItem.itemId === specItemId
      )
    );
  });

  // 如果有满足条件的SKU，则更新规格的可选状态
  if (matchingSkus.length > 0) {
    matchingSkus.forEach((sku: any) => {
      let disabled = true;
      if (sku.stockQty != 0) {
        disabled = false;
      }
      const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
      lastSpecList.items.forEach((item: any) => {
        if (item.itemId == matchingItemId) {
          item.disabled = disabled;
        }
      });
    });
  } else {
    lastSpecList.items.forEach((item: any) => {
      item.disabled = true;
    });
  }
}

// 根据当前选中的规格 高亮选中的sku
function isSelectedSku(sku: any) {
  // 获取当前 SKU 的规格项 ID 数组
  const specItemIds = sku.specItems.map((item: any) => item.itemId);
  // 检查当前 SKU 的所有规格项是否都在选中的列表中
  const allSpecsSelected = specItemIds.every((itemId: any) =>
    editForm.selectedSpecList.includes(itemId)
  );
  // 返回检查结果
  return allSpecsSelected;
}

// 更新选中的skuList
function onUpdateSkuList(index: any) {
  goodsDetail.skuList.forEach((sku: any) => {
    // 检查当前SKU是否满足已选规格条件
    const allSpecsSelected = sku.specItems.every((spec: any) =>
      editForm.selectedSpecList.includes(spec.itemId)
    );
    if (allSpecsSelected) {
      sku.skuName = sku.specItems.map((spec: any) => spec.itemName).join(",");
      // 添加首个sku时 如果起订量大于一次加购量 则取起订量 且是一次加购量的整数倍 否则则取一次加购量
      if (
        editForm.skuList.length == 0 &&
        sku.cartQty === 0 &&
        goodsDetail.minBuyQuantity > sku.minIncreaseQuantity
      ) {
        sku.cartQty =
          Math.ceil(goodsDetail.minBuyQuantity / sku.minIncreaseQuantity) *
          sku.minIncreaseQuantity;
      } else {
        sku.cartQty = sku.cartQty === 0 ? sku.minIncreaseQuantity : sku.cartQty;
      }
      // 检查是否已存在相同的SKU，如果不存在则添加，存在则更新
      const existingIndex = editForm.skuList.findIndex((existingSku: any) =>
        existingSku.specItems.every((spec: any) =>
          sku.specItems.some((newSpec: any) => newSpec.itemId === spec.itemId)
        )
      );

      if (existingIndex == -1) {
        editForm.skuList.push({ ...sku });
      }
    }
  });
  onCountFirstSkuTotal();
  onUpdateSkuPrice();
  if (index !== -1) {
    onUpdateSelectedSpec();
  }
}

// 获取第一规格的sku总和
function onCountFirstSkuTotal() {
  editForm.firstSpecTotals = {};
  // 遍历所有 SKU
  editForm.skuList.forEach((sku: any) => {
    // 获取第一规格的 itemId
    const firstSpecItemId = sku.specItems[0].itemId;
    // 如果该 itemId 在 firstSpecTotals 中不存在，则初始化为 0
    if (!editForm.firstSpecTotals[firstSpecItemId]) {
      editForm.firstSpecTotals[firstSpecItemId] = 0;
    }
    // 将当前 SKU 的数量累加到对应的第一规格数量之和中
    editForm.firstSpecTotals[firstSpecItemId] += sku.cartQty;
  });
}

// 销售规格的加购数修改
function onCartQtyUpdata(value: any, skuInfo: any) {
  if (value) {
    skuInfo.cartQty =
      Math.ceil(value / skuInfo.minIncreaseQuantity) *
      skuInfo.minIncreaseQuantity;
  }

  const skuName = skuInfo.specItems
    .map((spec: any) => spec.itemName)
    .join(", ");
  const remark = `商品编码：${goodsDetail.goodsNo}；属性组合名称：${skuName}；数量：${skuInfo.cartQty}`;
  window?.MyStat?.addPageEvent("click_sku_quantity", remark); // 埋点

  if (value === 0) {
    // 数量减为0 移除 editForm.skuList 中的SKU
    editForm.skuList = editForm.skuList.filter(
      (sku: any) => sku.id !== skuInfo.id
    );
    // 移除当前选中的规格
    const specItemIds = skuInfo.specItems.map((item: any) => item.itemId);
    // 检查当前 SKU 的所有规格项是否都在选中的列表中
    const allSpecsSelected = specItemIds.every((itemId: any) =>
      editForm.selectedSpecList.includes(itemId)
    );
    if (allSpecsSelected) {
      const spe = skuInfo.specItems.find((spec: any, index: any) => {
        if (index == skuInfo.specItems.length - 1) {
          return spec;
        }
      });
      const existingIndex = editForm.selectedSpecList.indexOf(spe.itemId);
      if (existingIndex !== -1) {
        editForm.selectedSpecList.splice(existingIndex, 1);
      }
    }
  }
  onUpdateSkuPrice();
  onUpdateSelectedSpec();
  onCountFirstSkuTotal();
}

// sku数组更新时 更新选中的规格
function onUpdateSelectedSpec() {
  // 移除不再存在于其他 SKU 中的规格
  editForm.selectedSpecList = editForm.selectedSpecList.filter(
    (specId: any) => {
      return editForm.skuList.some((sku: any) => {
        return sku.specItems.some((spec: any) => spec.itemId === specId);
      });
    }
  );
}

// 当前sku总和改变 更新sku的阶梯价格
function onUpdateSkuPrice() {
  const skuTotalQuantity = editForm.skuList.reduce(
    (total: number, sku: any) => {
      return total + sku.cartQty;
    },
    0
  );
  editForm.skuList.forEach((sku: any) => {
    for (const stepPrice of sku.stepPrices) {
      if (skuTotalQuantity <= stepPrice.end || stepPrice.end === -1) {
        sku.price = stepPrice.price;
        editForm.currentStepPriceEnd = stepPrice.end;
        break;
      }
    }
  });
}

// 计算预估运费
async function onCalculateEstimateFreight() {
  const spm = window.MyStat.getLocationParam("spm");
  const result: any = await useCalculateEstimateFreight({
    skuList: editForm.skuList.map((sku: any) => {
      return { skuId: sku.id, quantity: sku.cartQty, spm };
    }),
    siteId: window?.siteData?.siteInfo?.id,
    routeId: editForm.selectedRouteId || null, // 当前页面选中的线路（不传，则取默认值）
  });
  if (result.result?.code === 200) {
    editForm.totalEstimateFreight = result.data?.totalEstimateFreight || null;
    editForm.freightGoodsQty = result.data?.freightGoodsQty || null;
    editForm.routeList = result.data?.routeList || [];
    editForm.selectedRoute =
      editForm.routeList?.find((route: any) => route.selected) || {};
    editForm.selectedRouteId = editForm.selectedRoute?.routeId || null;
    // 计算总计&单价
    onCalculatePriceDetails();
  }
}

function onCalculatePriceDetails() {
  const productCost = Math.round(editForm.totalMoneyAmount * 100);
  const shippingCost = Math.round(
    (editForm.selectedRoute?.totalEstimateFreight || 0) * 100
  );
  const totalCostInCents = productCost + shippingCost;
  editForm.totalCost = Number((totalCostInCents / 100).toFixed(2));
  if (editForm.totalSpecItem > 0) {
    const unitPriceInCents = Math.round(
      (totalCostInCents * 100) / (editForm.totalSpecItem * 100)
    );
    editForm.unitPrice = Number((unitPriceInCents / 100).toFixed(2));
  } else {
    editForm.unitPrice = 0;
  }
}

// 提交前的检查
function onCheckBeforeSubmit() {
  // 未有选中的sku时 点击购物车报错提示的顺序（规格的展示在空间上顺序）
  let missingSpec = null;
  for (let spec of goodsDetail.specList) {
    let itemsSelected = spec.items.some((item: any) =>
      editForm.selectedSpecList.includes(item.itemId)
    );
    if (!itemsSelected) {
      missingSpec = spec.name;
      break;
    }
  }

  if (!editForm.skuList.length) {
    if (missingSpec) {
      showToast(`${authStore.i18n("cm_goods.pleaseSelect")} ${missingSpec}`);
    }
    return false;
  }

  if (editForm.totalSpecItem < goodsDetail.minBuyQuantity) {
    const error = authStore.i18n("cm_goodsDetail.chooseSpecTip");
    editForm.errorMessage = `${error}${goodsDetail.minBuyQuantity})`;
    setTimeout(() => {
      editForm.errorMessage = null;
    }, 3000);
    return false;
  }
  return true;
}

// 加购
async function onAddCart() {
  const checkPassed = onCheckBeforeSubmit();
  if (!checkPassed) return;

  const skuGroup = editForm.skuList
    .map((sku: any) => `属性组合名称：${sku.skuName}；数量：${sku.cartQty}`)
    .join("；");
  const remark = `商品编码：${
    goodsDetail.goodsNo
  }；${skuGroup}；是否大于限购：${
    editForm.totalSpecItem >= goodsDetail.minBuyQuantity ? "是" : "否"
  }`;
  window?.MyStat?.addPageEvent("click_sku_add", remark); // 埋点

  const skus = editForm.skuList.map((sku: any) => {
    return {
      skuId: sku.id,
      quantity: sku.cartQty,
      spm: props.goods.spm,
    };
  });
  const params = {
    goodsId: goodsDetail.id,
    skus,
    siteId: window?.siteData?.siteInfo?.id,
    routeId: editForm.selectedRouteId,
    padc: goodsDetail.padc,
  };

  if (!!window?.fbq) {
    window?.fbq("track", "AddToCart", {
      content_ids: [goodsDetail.id],
      content_name: goodsDetail?.goodsName,
      content_type: "product",
      value: setUnit(editForm.totalMoneyAmount),
      currency: "USD",
      contents: skus,
    });
  }
  if (!!window?.ttq) {
    window?.ttq?.track("AddToCart", {
      currency: "USD",
      value: editForm.totalMoneyAmount,
      content_type: "product",
      description: JSON.stringify(skus),
    });
  }
  if (!!window?.gtag) {
    window?.gtag("event", "AddToCart", {
      content_ids: [goodsDetail.id],
      content_name: goodsDetail?.goodsName,
      content_type: "product",
      value: setUnit(editForm.totalMoneyAmount),
      currency: "USD",
      contents: skus,
    });
  }
  // 未登录 去登陆 登录成功后再次调用加购接口
  if (isEmptyObject(userInfo.value)) {
    let pageSource = window.location.href;
    navigateToPage(
      "/h5/user/login",
      {
        pageSource: pageSource,
        goods: JSON.stringify(params),
        spm: props.goods.spm,
      },
      false
    );
    return;
  }

  const result: any = await useAddCart(params);
  if (result.result?.code === 200) {
    showToast(
      authStore.i18n("cm_goods.addToList"),
      1500,
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/11/df87d5e1-099d-48ca-97a4-239bb000748a.png"
    );
    authStore.getCartList();
    emit("onUpdateList", goodsDetail.id);
  } else {
    editForm.errorMessage = result.result?.message;
    setTimeout(() => {
      editForm.errorMessage = null;
    }, 3000);
  }
}

function onFindListClick(event: any) {
  // 未登录 去登陆
  if (isEmptyObject(userInfo.value)) {
    navigateToPage("/h5/user/login", { pageSource: "/h5/find" }, false, event);
  } else {
    navigateToPage("/h5/find", {}, false, event);
  }
  pageData.dialogVisible = false;
  emit("onCloseDetail");
}

function filterPriceRange(price: any) {
  if (price.end == -1) {
    return `>=${price.start}`;
  } else {
    if (price.start == price.end) {
      return `${price.start}`;
    }
    return `${price.start}-${price.end}`;
  }
}

function onCloseDetail() {
  pageData.dialogVisible = false;
  emit("onCloseDetail");
}
async function onGoFindSubmit() {
  window?.MyStat?.addPageEvent(
    "detail_click_inquiry",
    `商品详情页进入询盘提交页`
  ); // 埋点

  const checkPassed = onCheckBeforeSubmit();
  if (!checkPassed) return;

  const skus = editForm.skuList.map((sku: any) => {
    return { skuId: sku.id, quantity: sku.cartQty, spm: props.goods.spm };
  });
  if (!!window?.fbq) {
    window?.fbq("track", "InitiateCheckout", {
      currency: "USD",
      num_items: skus?.length,
      contents: skus,
    });
  }
  if (!!window?.ttq) {
    window?.ttq?.track("InitiateCheckout", {
      currency: "USD",
      value: editForm.totalMoneyAmount,
      content_type: "product",
      description: JSON.stringify(skus),
    });
  }

  const res: any = await useGetInquiry({ params: skus });
  if (res?.result?.code === 200) {
    const inquiryInfo = res?.data;
    await authStore.setInquiryInfo(inquiryInfo);
    window.location.href = `/h5/find/submit?fromCart=${false}`;
  } else {
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}
function onGoHome() {
  window.location.href = "/h5";
}

// 保存选择的国家
const onSaveCountry = () => {
  onGetGoodsDetail("updateShippingCost");
  if (editForm.skuList.length) {
    onCalculateEstimateFreight();
  }
};

// 选择路线方法
const onSelectRoute = (route: any) => {
  editForm.selectedRoute = route;
  editForm.selectedRouteId = route.routeId;
  onCalculatePriceDetails();
  // 确保只有一条路线被选中
  editForm.routeList.forEach((r: any) => {
    r.selected = r.routeId === route.routeId;
  });
  window?.MyStat?.addPageEvent(
    "select_goods_route",
    `选择了商品（${goodsDetail.goodsNo}）线路：${
      route.routeName
    }，运费：${setUnit(route.partEstimateFreight)} por ${
      route.freightGoodsQty
    } ${goodsDetail.goodsPriceUnitName}`
  );
};
</script>

<style scoped lang="scss">
.spec-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 0.8rem;
  margin: 0.2rem 0.38rem 0.13rem 0;
  padding: 0 0.16rem;
  min-width: 0.8rem;
  border: 1px solid #d7d7d7;
  cursor: pointer;
  position: relative;
  .fixed-count {
    position: absolute;
    right: -0.28rem;
    top: -0.2rem;
    color: #fff;
    background: #e50113;
    border-radius: 0.16rem;
    padding: 0.02rem 0.06rem;
    font-size: 0.24rem;
  }
}
.selected-btn {
  border: 1px solid #e50113;
}
.no-stock-btn {
  background: #f2f2f2 !important;
  cursor: not-allowed !important;
}
:deep(.n-dialog .n-dialog__icon) {
  display: none !important;
}
.border-btn {
  border: 1px solid #c7c7c7;
}
.sku-table {
  td {
    padding: 0.16rem;
  }
}
.sku-table tr.selected {
  border: 1px solid #e50113;
}
:deep(.n-input__input-el) {
  text-align: center;
}
.submit-btn {
  height: 0.8rem;
  line-height: 0.8rem;
  color: #fff;
  background: linear-gradient(270deg, #fc573f, #f12d2a, #e50113);
  text-align: center;
}
:deep(.n-drawer-footer) {
  padding: 0 !important;
}
:deep(.n-drawer-header) {
  padding: 0.2rem 0.2rem !important;
  .n-drawer-header__main {
    margin: 0 auto;
  }
}
:deep(.n-drawer-body-content-wrapper) {
  padding: 0 !important;
}
.error-drawer {
  :deep(.n-drawer-body) {
    padding-bottom: 1rem !important;
  }
}
.normal-drawer {
  :deep(.n-drawer-body) {
    padding-bottom: 0 !important;
  }
}

.scrollable-container {
  overflow-x: auto;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.scrollable-container::-webkit-scrollbar {
  display: none;
}
.price-container {
  padding: 0.2rem;
  position: relative;
  background: #f6f6f6;
}
.price-container::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 0.4rem;
  height: 100%;
  background: #f6f6f6;
  z-index: 9;
}
:deep(.n-base-icon) {
  height: 0.36rem;
  width: 0.36rem;
  line-height: 0.36rem;
  svg {
    height: 0.36rem;
    width: 0.36rem;
  }
}
</style>
