<template>
  <div class="page-wrapper">
    <div
      class="w-[1280px] mx-auto flex justify-end pr-[40px] pt-[126px] relative"
    >
      <div class="page-title">
        {{ authStore.i18n("cm_common.inviteNewClients") }}
      </div>
      <img
        loading="lazy"
        src="@/assets/icons/loginPart.png"
        alt="login"
        class="w-[840px] absolute top-[250px] right-[432px]"
      />
      <div
        class="bg-white w-[530px] rounded-[8px] px-[80px] py-[32px] flex-shrink-0 relative z-1"
      >
        <div
          class="text-center font-medium text-[30px] leading-[30px] mb-[36px]"
        >
          {{ authStore.i18n("cm_common_forgotPassword") }}
        </div>
        <!-- 忘记密码 -->
        <n-form
          :rules="modifyPwdRules"
          ref="modifyPwdFormRef"
          :model="pageData.modifyPwdForm"
        >
          <n-form-item path="email" :label="authStore.i18n('cm_common.email')">
            <n-input
              v-trim
              clearable
              class="h-[52px]"
              @keydown.enter.prevent
              @blur="onBlurEvent(pageData.modifyPwdForm.email, 'email')"
              v-model:value="pageData.modifyPwdForm.email"
              :placeholder="authStore.i18n('cm_common.inputEmail')"
            >
              <template #prefix>
                <img
                  loading="lazy"
                  alt="email"
                  class="mr-1.5 w-[22px]"
                  src="@/assets/icons/email.svg"
                />
              </template>
            </n-input>
          </n-form-item>
          <n-form-item
            path="password"
            :label="authStore.i18n('cm_common.password')"
          >
            <n-input
              v-trim
              clearable
              class="h-[52px]"
              @keydown.enter.prevent
              @blur="onBlurEvent(pageData.modifyPwdForm.password, 'password')"
              :type="pageData.showModifyPwd ? 'text' : 'password'"
              v-model:value="pageData.modifyPwdForm.password"
              :placeholder="authStore.i18n('cm_common.inputPassword')"
            >
              <template #prefix>
                <img
                  loading="lazy"
                  alt="lock"
                  class="mr-1.5 w-[22px]"
                  src="@/assets/icons/lock.svg"
                />
              </template>
              <template #suffix>
                <icon-card
                  size="24"
                  color="#7F7F7F"
                  class="cursor-pointer"
                  :name="
                    pageData.showModifyPwd
                      ? 'weui:eyes-on-outlined'
                      : 'weui:eyes-off-outlined'
                  "
                  @click="pageData.showModifyPwd = !pageData.showModifyPwd"
                /> </template
            ></n-input>
          </n-form-item>
          <n-form-item
            path="captcha"
            :label="authStore.i18n('cm_common.verification')"
          >
            <n-input
              v-trim
              clearable
              class="h-[52px]"
              @keydown.enter.prevent
              @blur="onBlurEvent(pageData.modifyPwdForm.captcha, 'captcha')"
              v-model:value="pageData.modifyPwdForm.captcha"
              :placeholder="authStore.i18n('cm_common.inputVerification')"
            >
              <template #suffix>
                <n-button
                  ghost
                  secondary
                  color="#FF6B81"
                  :disabled="pageData.timeLeft > 0"
                  @click="onCountDownClick"
                  class="p-0"
                >
                  {{
                    pageData.timeLeft > 0
                      ? pageData.timeLeft + "s"
                      : authStore.i18n("cm_common.sendCode")
                  }}
                </n-button>
              </template></n-input
            >
          </n-form-item>
          <div class="flex justify-end">
            <div
              class="text-[#e50113] cursor-pointer text-[17px] leading-[17px]"
              @click="onGoLogin"
            >
              {{ authStore.i18n("cm_common_login") }}
            </div>
          </div>
          <n-form-item>
            <n-button
              size="large"
              color="#E50113"
              text-color="#fff"
              class="rounded-[8px] px-[0.16rem] w-full h-[52px] text-[16px] leading-[16px]"
              @click="onModifyPwd"
            >
              {{ authStore.i18n("cm_common.sendingAndLogging") }}
            </n-button>
          </n-form-item>
          <div class="text-[12px]">
            <div>{{ authStore.i18n("cm_common.noSendCode") }}</div>
            <ul class="ml-[0.48rem] mt-[0.08rem]">
              <li>
                {{ authStore.i18n("cm_common.sendCodeTime") }}
              </li>
              <li>
                {{ authStore.i18n("cm_common.checkEmail") }}
              </li>
            </ul>
          </div>
        </n-form>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import type { FormInst, FormItemRule, FormRules } from "naive-ui";

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
authStore.setShowAnchor(false);
authStore.setShowCarousel(false);

const modifyPwdFormRef = ref<FormInst | null>(null);
const pageData = reactive({
  showModifyPwd: false,
  modifyPwdForm: <any>{
    email: "",
    password: "",
    captcha: "",
  },
  addCartGoods: {},
  timeLeft: 0,
});

const modifyPwdRules: FormRules = {
  email: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any) {
      const pattern = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/;
      if (!value) {
        return new Error(authStore.i18n("cm_common.inputEmailTips"));
      } else if (!pattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_email_format_error",
          `邮箱格式错误，邮箱：${value}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.emailTips"));
      }
      return true;
    },
  },
  password: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any) {
      const specialCharPattern = /[^A-Za-z\d]/; // 特殊字符匹配
      const pattern = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$/; // 8-16位包含数字及字母
      if (!value) {
        return new Error(authStore.i18n("cm_common.inputPwdTips"));
      }
      // 校验是否包含特殊字符
      else if (specialCharPattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_password_format_error",
          `密码格式错误：${authStore.i18n("cm_common.pwdFormatTips")}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.pwdFormatTips"));
      }
      // 校验8-16位包含数字及字母
      else if (!pattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_password_format_error",
          `密码格式错误：${authStore.i18n("cm_common.pwdLengthTips")}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.pwdLengthTips"));
      }
      return true;
    },
  },
  captcha: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_common.verificationTips"),
  },
};

onMounted(() => {
  let loginInfo = sessionStorage.getItem("loginInfo");
  if (loginInfo) {
    const info = JSON.parse(loginInfo);
    pageData.modifyPwdForm.email = info.username;
    pageData.modifyPwdForm.password = info.password;
    sessionStorage.removeItem("loginInfo");
  }
});

// 校验码
async function onCountDownClick() {
  window?.MyStat?.addPageEvent(
    "passport_send_verify_code",
    "在账号窗口，点击发送验证码"
  );
  const res: any = await useSendCaptcha({ str: pageData.modifyPwdForm.email });
  if (res.result?.code == 200) {
    pageData.timeLeft = 60;
    const countdown = setInterval(() => {
      if (pageData.timeLeft === 0) {
        clearInterval(countdown);
      } else {
        pageData.timeLeft--;
      }
    }, 1000);
    showToast(authStore.i18n("cm_common_sendSuccess"));
  } else {
    showToast(res.result?.message);
    window?.MyStat?.addPageEvent(
      "passport_send_verify_code_error",
      `发送验证码错误：${res.result?.message}`
    );
  }
}
// 修改密码
async function onModifyPwd() {
  await modifyPwdFormRef.value?.validate((errors: any) => {
    errors?.length &&
      window?.MyStat?.addPageEvent(
        "passport_forgot_verify_fail",
        `忘记密码表单校验不通过：${errors.map((item: any) =>
          item.map((it: any) => it.message)
        )}`
      );
  });
  const res: any = await useModifyPassword(pageData.modifyPwdForm);
  if (res?.result?.code === 200) {
    showToast(authStore.i18n("cm_common_modifySuccess"));
    setTimeout(() => {
      // 密码修改成功之后登录
      onLogin({
        username: pageData.modifyPwdForm.email,
        password: pageData.modifyPwdForm.password,
      });
      authStore.setUserInfo(res?.result?.data);
    }, 3000);
  } else {
    showToast(res.result?.message);
    window?.MyStat?.addPageEvent(
      "passport_forgot_submit_error",
      `忘记密码提交错误：${res.result?.message}`
    );
  }
}

// 登录
async function onLogin(params: any) {
  try {
    const res: any = await useLogin(params);
    if (res?.result?.code === 200) {
      await authStore.setUserInfo(res?.data);
      await onAddCart();
      if (!!window?.gtag) {
        window?.gtag("event", "LoginSuccess");
      }
      window.location.replace(route.query?.pageSource || "/");
    } else {
      window?.MyStat?.addPageEvent(
        "passport_login_submit_error",
        `登录提交错误：${res.result?.message}`
      );
    }
  } catch (error) {
    showToast(error);
  }
}

// 加购
async function onAddCart() {
  if (!route.query.goods) return;
  const res: any = await useAddCart(JSON.parse(route.query.goods));
  if (res.result?.code === 200) {
    showToast(
      authStore.i18n("cm_goods.addToList"),
      1500,
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/11/df87d5e1-099d-48ca-97a4-239bb000748a.png"
    );
    authStore.getCartList();
  } else {
    showToast(res.result?.message);
  }
}

// 去登录
function onGoLogin() {
  const loginInfo = {
    username: pageData.modifyPwdForm.email,
    password: pageData.modifyPwdForm.password,
  };
  sessionStorage.setItem("loginInfo", JSON.stringify(loginInfo));
  window?.MyStat?.addPageEvent("passport_switch_login", "切换到登录TAB");
  router.go(-1);
}

function onBlurEvent(val: any, type: string) {
  if (!val) return;
  const eventMap: { [key: string]: { event: string; message: string } } = {
    email: {
      event: "passport_input_email",
      message: "在账号窗口，输入了邮箱",
    },
    password: {
      event: "passport_input_password",
      message: "在账号窗口，输入了密码",
    },
    invite: {
      event: "passport_input_invite",
      message: "在账号窗口，输入了邀请码",
    },
    captcha: {
      event: "passport_input_verify_code",
      message: "在账号窗口，输入了验证码",
    },
  };

  const eventInfo = eventMap[type];
  if (eventInfo) {
    window?.MyStat?.addPageEvent(eventInfo.event, eventInfo.message);
  }
}
</script>
<style scoped lang="scss">
:deep(.n-input__input-el) {
  height: 52px;
}
:deep(.n-form-item-label) {
  font-size: 16px;
  line-height: 16px;
}
.page-wrapper {
  width: 100%;
  height: 100vh;
  min-width: 1280px;
  min-height: 750px;
  background-repeat: no-repeat;
  background-image: url("@/assets/icons/newLoginBg.png");
  background-size: cover;
  overflow: hidden;
}

.page-title {
  width: 540px;
  position: relative;
  text-align: center;
  z-index: 1;
  margin-right: 64px;
  margin-top: 10px;
  font-size: 48px;
  font-style: italic;
  font-weight: 600;
  line-height: 48px;
  text-shadow: 2px 4px 8px rgba(229, 1, 19, 0.9);
  color: #fff4d5;
}

:deep(.n-input, .n-input--focus) {
  --n-box-shadow-focus: none;
  --n-box-shadow-focus-error: none;
  --n-border-warning: none;
  --n-border-focus-warning: none;
  --n-border-hover-warning: none;
  --n-border: none;
  --n-border-disabled: none;
  --n-border-hover: none;
  --n-border-focus: none;
  background-color: #f2f2f2 !important;
  border-radius: 8px;
}
:deep(.n-input--focus) {
  background-color: #f2f2f2;
}
ul {
  list-style-type: disc;
}
:deep(.n-input__placeholder) {
  font-size: 16px;
  line-height: 16px;
}
</style>
