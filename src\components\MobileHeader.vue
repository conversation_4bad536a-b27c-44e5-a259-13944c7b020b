<template>
  <div
    ref="headerRef"
    class="flex border-b-1 border-solid border-gray-200 header-bar h-[0.9rem] flex justify-center items-center"
    :style="{ top: showCarousel ? '0.8rem' : '0' }"
  >
    <a
      class="flex justify-center items-center"
      href="/h5"
      data-spm-box="navigation-logo-icon"
    >
      <n-image
        lazy
        preview-disabled
        object-fit="fill"
        :src="pageTheme.logo"
        class="w-[3rem]"
        :img-props="{ referrerpolicy: 'no-referrer' }"
      />
    </a>
    <div class="absolute right-[0.24rem]">
      <n-space
        :style="{ gap: '0 0.12rem' }"
        class="flex justify-center items-center"
      >
        <div class="w-[0.52rem] h-[0.52rem]">
          <image-search>
            <img
              loading="lazy"
              class="w-[0.52rem]"
              alt="image-search"
              src="@/assets/icons/imageSearchAc.svg"
              referrerpolicy="no-referrer"
            />
          </image-search>
        </div>

        <a href="/h5/search" data-spm-box="navigation-search-icon">
          <img
            loading="lazy"
            alt="text-search"
            class="w-[0.52rem]"
            src="@/assets/icons/searchAc.svg"
            referrerpolicy="no-referrer"
          />
        </a>
        <div
          class="mobile-cart-btn"
          @click="onFindListClick($event)"
          data-spm-box="navigation-cart-icon"
        >
          <img
            loading="lazy"
            class="w-[0.52rem]"
            alt="cart"
            src="@/assets/icons/cart.svg"
            referrerpolicy="no-referrer"
          />
          <div v-if="cartGoodsCount" class="fixed-count">
            {{ cartGoodsCount }}
          </div>
        </div>
      </n-space>
    </div>
  </div>
  <!-- 占位元素 -->
  <div class="h-[0.9rem]"></div>
</template>

<script setup lang="ts" name="MobileHeader">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
const route = useRoute();
const authStore = useAuthStore();
const headerRef = ref<any>(null);
const userInfo = computed(() => useAuthStore().getUserInfo);
const showCarousel = computed(() => useAuthStore().getShowCarousel);

const props = defineProps({
  isShowHeaderBtn: {
    type: Boolean,
    default: true,
  },
});
const pageTheme = computed(() => useConfigStore().getPageTheme);
const cartGoodsCount = computed(() => {
  const count = authStore.$state?.cartStat?.goodsCount;
  return count && count > 99 ? "99+" : count;
});
const pageData = reactive({
  showBtn: true,
});

watch(
  () => props.isShowHeaderBtn,
  (newVal: any) => {
    pageData.showBtn = newVal;
  },
  { immediate: true }
);

function onFindListClick(event: any) {
  // 未登录 去登陆
  if (isEmptyObject(userInfo.value)) {
    navigateToPage("/h5/user/login", { pageSource: "/h5/find" }, false, event);
  } else {
    navigateToPage("/h5/find", {}, false, event);
  }
}
</script>

<style scoped lang="scss">
.header-bar {
  width: 100%;
  position: fixed;
  top: 0.8rem;
  left: 0;
  z-index: 10;
  background-color: #fff;
}
</style>
