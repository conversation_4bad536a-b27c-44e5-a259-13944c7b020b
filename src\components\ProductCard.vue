<template>
  <a
    @click="onLinkTo"
    :href="pageData.linkUrl"
    :data-spm-box="props.pageSource"
    :data-spm-index="props.goodsIndex + 1"
    :data-spm-param="route?.query?.tagId || null"
  >
    <n-card :bordered="true" class="p-1 hover:cursor-pointer text-black">
      <div class="flex flex-col">
        <n-image
          lazy
          preview-disabled
          :img-props="{ referrerpolicy: 'no-referrer' }"
          :src="goods.mainImageUrl"
          :intersection-observer-options="{
            root: '#left-wrapper',
          }"
          class="enlarge-image w-[250px] h-[250px] mx-auto mb-2"
        >
          <template #placeholder>
            <n-skeleton
              :text="true"
              height="250px"
              width="250px"
              style="margin-bottom: 3px"
            />
          </template>
        </n-image>
        <n-ellipsis
          :line-clamp="2"
          class="text-sm h-10 mb-2"
          :tooltip="false"
          v-if="pageData.type !== 'imgSearch'"
        >
          {{ goods.goodsName }}
        </n-ellipsis>
        <template v-if="pageData.type !== 'imgSearch'">
          <n-text class="text-base mb-2">
            <span class="font-semibold">
              <!-- 规格单价的最小值和最大值展示；若相同则只展示一个价格值 -->
              <!-- <span v-if="goods.minPrice != goods.maxPrice"
                >{{ setUnit(goods.minPrice) }} - {{ goods.maxPrice }}</span
              >
              <span v-else>{{ setUnit(goods.maxPrice) }}</span> -->
              <span>{{ setUnit(goods.minPrice) }}</span>
            </span>
            <span class="text-[13px] text-gray-400 ml-1"
              >/ {{ goods.goodsPriceUnitName }}</span
            >
          </n-text>
          <!-- <n-text class="mb-2 text-sm">
            <span
              >{{ goods.minBuyQuantity }} {{ goods.goodsPriceUnitName }}</span
            >
            <span class="text-gray-400 ml-1 text-[13px]">(MOQ)</span>
          </n-text> -->
          <!-- 预估运费 -->
          <div v-if="goods.pcsEstimateFreight" class="text-[12px] mb-3">
            {{ authStore.i18n("cm_goods.shippingCost") }}
            <span class="text-[14px]">{{
              setUnit(goods.pcsEstimateFreight)
            }}</span>
            /
            {{ goods?.goodsPriceUnitName }}
          </div>
        </template>
        <span v-else class="text-base font-semibold mb-3"
          >{{ authStore.i18n("cm_goods_atLeast") }}
          {{ setUnit(goods.minPrice) }}</span
        >
        <n-button
          color="#fff"
          text-color="#000"
          class="add-btn"
          @click.stop.prevent="onAddCart($event)"
        >
          <icon-card size="20" name="f7:cart" color="#666"></icon-card>
          <div class="text-xs ml-2">
            {{ authStore.i18n("cm_find_addCart") }}
          </div>
          <icon-card
            size="24"
            name="material-symbols:check-circle"
            class="add-btn-check"
            color="#e50113"
            v-if="goods.selected"
          ></icon-card>
        </n-button>
      </div>
    </n-card>
  </a>
</template>

<script setup lang="ts" name="ProductCard">
import { useAuthStore } from "@/stores/authStore";
import { goodsDetailPCPath } from "@/utils/constant";
const route = useRoute();
const authStore = useAuthStore();
const emit = defineEmits([
  "onOpenDetail",
  "onUpdateGoodsId",
  "onUpdateLoading",
]);

const props = defineProps({
  goods: {
    type: Object,
    default: () => {},
  },
  discountRate: {
    type: String,
    default: "",
  },
  goodsIndex: {
    type: Number,
    default: 0,
  },
  pageSource: {
    type: String,
    default: "",
  },
});

const pageData = reactive(<any>{
  type: route.query.type || "",
  linkUrl: "",
});

// 获取商品详情链接
const getLinkUrl = async (): Promise<string | null> => {
  let goodsId = props.goods.goodsId;
  if (!goodsId) {
    emit("onUpdateLoading", true);
    const res: any = await useGetGoods({ str: props.goods.sourceGoodsId });
    emit("onUpdateLoading", false);

    if (res?.result?.code === 200) {
      goodsId = res.data;
      emit("onUpdateGoodsId", props.goodsIndex, goodsId);
    } else {
      showToast(authStore.i18n("cm_common_addGoodsError"));
      return null;
    }
  }
  return `${goodsDetailPCPath}/${goodsId}`;
};

// 跳转详情页
const onLinkTo = async (event: MouseEvent) => {
  event.preventDefault();
  const url = await getLinkUrl();
  if (!url) return;

  const params: Record<string, string> = {};
  if (props.goods.padc) params.padc = props.goods.padc;
  if (route.query.primaryCid || route.params.id) {
    params.primaryCid = String(route.query.primaryCid || route.params.id);
  }

  navigateToPage(url, params, true, event);

  // 埋点
  window?.MyStat?.addPageEvent?.(
    "click_goods_detail",
    `商品编码：${props.goods.goodsNo || "N/A"}`
  );
};

async function onAddCart(e: any) {
  emit("onOpenDetail", e, props.goodsIndex);
}
</script>

<style scoped lang="scss">
.n-card :deep(.n-card__content) {
  padding: 0.5rem 0.5rem 0.5rem 0.5rem;
}

.overlay-icon {
  position: absolute;
  top: 15px;
  left: 20px;
  transform: translate(-60%, -23%);
  width: 52px;
  height: 78px;
  padding: 0.5rem 0.25rem;
  background-color: #ffff00;
}
.add-btn {
  width: 98%;
  margin: 0 auto;
  height: 36px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  border-radius: 30px;
  box-sizing: border-box;
  color: #2d2d2d;
  border: 1px solid #464646;
  background: none;
  cursor: pointer;
  &:hover {
    background: linear-gradient(270deg, #fc573f, #e50113);
    color: #fff;
    border: none;
  }
  .add-btn-check {
    position: absolute;
    right: -6px;
    top: -10px;
    background-color: #fff;
    border-radius: 50%;
  }
}
</style>
