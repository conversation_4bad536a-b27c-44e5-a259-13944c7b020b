<template>
  <div class="container">
    <div class="border-b border-[#EFEFF5]">
      <search-card></search-card>
    </div>

    <div class="cwidth flex mx-auto mt-[34px] px-[20px]">
      <div class="flex-1 mr-[18px]">
        <div class="flex items-center justify-between">
          <div
            class="text-[28px] leading-[28px] font-medium mb-[32px] shrink-0"
          >
            {{ authStore.i18n("cm_find.inquiryList") }}
          </div>
        </div>
        <div v-if="!pageData.loading">
          <div v-if="pageData.goodsList.length">
            <!-- 一级 -->
            <n-checkbox
              size="large"
              v-model:checked="pageData.selectAll"
              class="text-[16px] leading-[16px] font-medium mb-[28px] w-full"
              @update:checked="onAllSelection"
              ><n-divider class="divider" title-placement="left"
                >{{ authStore.i18n("cm_find.selectAllItems") }} (<span>{{
                  pageData.stat.skuCount
                }}</span
                >)</n-divider
              ></n-checkbox
            >
            <!-- 二级 -->
            <div
              v-for="(goods, index) in pageData.goodsList"
              :key="goods.goodsId"
              class="mb-4"
            >
              <div class="flex">
                <n-checkbox
                  class="mr-2"
                  v-model:checked="goods.selected"
                  @update:checked="(value) => onGoodsSelection(value, goods)"
                >
                </n-checkbox>
                <goods-card
                  :goods="goods"
                  class="flex-1 mr-10"
                  :spmIndex="index"
                  spmCode="cart-goods-list"
                ></goods-card>
                <icon-card
                  name="uil:trash-alt"
                  color="#797979"
                  size="28"
                  @click="onDeleteGoods(goods)"
                ></icon-card>
              </div>
              <!-- 三级分类 -->
              <div
                v-for="sku in goods.skuList"
                :key="sku.skuId"
                class="sku-checkbox mb-3 ml-10"
              >
                <div class="flex items-center">
                  <n-checkbox
                    v-model:checked="sku.selected"
                    @update:checked="
                      (value) => onSkuSelection(value, sku, goods)
                    "
                    class="flex-1"
                  >
                  </n-checkbox>
                  <sku-card
                    :sku="sku"
                    :goods="goods"
                    @onCartQtyUpdate="onCartQtyUpdate"
                    :step="sku.minIncreaseQuantity"
                    ><icon-card
                      name="iconamoon:arrow-right-2"
                      color="#797979"
                      size="26"
                      class="ml-4 cursor-pointer"
                      @click.stop="onOpenSkuDialog(sku, goods)"
                    ></icon-card
                  ></sku-card>
                  <icon-card
                    name="uil:trash-alt"
                    color="#797979"
                    size="28"
                    @click="onDeleteSku(sku, goods)"
                  ></icon-card>
                </div>
              </div>
              <n-divider v-if="index !== pageData.goodsList.length - 1" />
            </div>
          </div>
          <n-empty
            v-else
            :description="authStore.i18n('cm_find.noData')"
            class="mt-24"
          >
            <template #extra>
              <n-button
                size="small"
                color="#E50113"
                text-color="#fff"
                @click="onGoHome"
              >
                {{ authStore.i18n("cm_find.goHome") }}</n-button
              >
            </template>
          </n-empty>
        </div>
        <div v-show="pageData.loading" class="loading-overlay">
          <n-spin stroke="#e50113" :show="pageData.loading"> </n-spin>
        </div>
      </div>
      <div>
        <div class="w-[400px]">
          <n-affix :trigger-top="20" id="submit-affix" class="h-fit">
            <div class="w-[400px] flex flex-col gap-[24px] px-[20px] py-[24px]">
              <div
                class="text-[18px] leading-[18px] font-medium text-[#1A1A1A]"
              >
                {{ authStore.i18n("cm_find_inquireSummary") }}
              </div>
              <country-select
                mode="popover"
                @save="onSaveCountry"
                spm="select_site_from_cart"
                class="find-country-select"
              >
              </country-select>
              <div class="flex justify-between text-[16px] leading-[16px]">
                <span>{{ authStore.i18n("cm_find_quantityOfUnits") }}</span>
                <span>
                  <span class="font-medium">{{
                    pageData.stat?.selectSkuTotalQuantity
                  }}</span>
                  {{
                    pageData.stat?.selectSkuTotalQuantity > 1
                      ? authStore.i18n("cm_find_totalSkuUnits")
                      : authStore.i18n("cm_find_totalSkuUnit")
                  }}
                </span>
              </div>
              <div class="flex justify-between text-[16px] leading-[16px]">
                <span>{{ authStore.i18n("cm_find_itemsCost") }}:</span
                ><span
                  class="text-[#e50113] font-medium"
                  v-if="
                    pageData.stat?.selectTotalSalePrice ||
                    pageData.stat?.selectTotalSalePrice === 0
                  "
                  >{{ setUnit(pageData.stat?.selectTotalSalePrice) }}</span
                >
              </div>
              <div
                class="flex justify-between text-[16px] leading-[16px] text-[#4D4D4D]"
              >
                <n-popover trigger="hover" raw>
                  <template #trigger>
                    <div class="flex items-center cursor-pointer">
                      <img
                        class="w-[14px] h-[14px] mr-[2px]"
                        src="@/assets/icons/common/alert-circle.svg"
                        :alt="authStore.i18n('cm_goods.estimatedShippingCost')"
                        referrerpolicy="no-referrer"
                      />
                      {{ authStore.i18n("cm_goods.estimatedShippingCost") }}:
                    </div>
                  </template>
                  <div
                    style="
                      z-index: 1;
                      width: 300px;
                      padding: 6px 14px;
                      background-color: #fff4d4;
                      transform-origin: inherit;
                      border: 1px solid #f7ba2a;
                    "
                  >
                    {{ authStore.i18n("cm_goods.freightAdjustmentPending") }}
                  </div>
                </n-popover>

                <span v-if="pageData?.totalEstimateFreight">{{
                  setUnit(pageData.totalEstimateFreight)
                }}</span>
                <span v-else>{{
                  authStore.i18n("cm_goods.pendingConfirmation")
                }}</span>
              </div>
            </div>
            <div class="w-[400px] mt-[16px]">
              <div
                class="text-[14px] leading-[14px] text-[#A6A6A6] text-center"
              >
                {{ authStore.i18n("cm_find_confirmWithoutPay") }}
              </div>

              <n-button
                size="large"
                color="#E50113"
                text-color="#fff"
                @click="onGoFindSubmit($event)"
                data-spm-box="cart-to-checkout"
                class="rounded-[6px] w-full h-[44px] my-[12px]"
              >
                <img
                  src="@/assets/icons/common/list.svg"
                  alt="list"
                  class="mr-[8px]"
                  referrerpolicy="no-referrer"
                />
                <span class="text-[16px] leading-[16px]">{{
                  authStore.i18n("cm_find.inquireNow")
                }}</span>
              </n-button>
              <div
                class="text-[14px] leading-[16px] text-[#A6A6A6] text-center"
              >
                {{ authStore.i18n("cm_find_submitTip") }}
              </div>
            </div>
          </n-affix>
        </div>
      </div>
    </div>
    <!-- 选择商品规格 -->
    <n-modal
      preset="dialog"
      :show-icon="false"
      :closable="false"
      :style="{ width: '600px' }"
      v-model:show="pageData.dialogVisible"
    >
      <!-- 商品规格 -->
      <div>
        <div
          v-for="spec in pageData.currentGoods.specList"
          :key="spec.id"
          class="ml-2 mt-3"
        >
          <div class="mb-4">{{ spec.name }}:</div>
          <div class="flex flex-wrap mb-4">
            <div
              v-for="item in spec.items"
              :key="item.itemId"
              @click="onSpecUpdate(spec.id, item.itemId, item)"
              class="spec-btn min-w-10 max-w-4/5 relative"
              :class="{
                'current-btn': pageData.selectedSpec[spec?.id] == item.itemId,
                'disabled-btn': item.disabled,
              }"
            >
              <n-image
                lazy
                preview-disabled
                object-fit="fill"
                class="w-[36px] h-[36px] shrink-0 mr-3"
                :src="item.imageUrl"
                v-if="item.imageUrl"
                :img-props="{ referrerpolicy: 'no-referrer' }"
              />
              <span class="py-2">{{ item.itemName }}</span>
              <icon-card
                size="20"
                name="typcn:tick"
                color="#fff"
                class="btn-tick mr-2"
              ></icon-card>
            </div>
          </div>
        </div>
        <n-divider />
        <div>
          <n-button
            @click="onConfirm"
            color="#E50113"
            class="mr-4"
            :disabled="!pageData.updatedSku"
            text-color="#fff"
            >{{ authStore.i18n("cm_find.confirm") }}</n-button
          >
          <n-button @click="onCancel">{{
            authStore.i18n("cm_find.cancel")
          }}</n-button>
        </div>
      </div>
    </n-modal>
    <!--询盘提交错误提示  -->
    <n-modal :show="pageData.errorDialogVisible" :show-icon="false">
      <n-card
        :bordered="false"
        style="
          width: 500px;
          color: #000;
          padding: 26px 0 !important;
          text-align: center;
        "
      >
        <div>
          <icon-card
            size="24"
            name="mingcute:warning-line"
            class="add-btn-check"
            color="#E50113"
          ></icon-card>
          {{ pageData.errorMessage }}
        </div>
        <n-button
          size="small"
          color="#E50113"
          text-color="#fff"
          round
          @click="onGoHome"
          class="mt-4"
        >
          {{ authStore.i18n("cm_find_shopAgain") }}</n-button
        >
      </n-card>
    </n-modal>
    <!-- 2000美元提交校验提示 -->
    <n-modal :show="pageData.submitDialogVisible" :show-icon="false">
      <n-card
        :bordered="false"
        style="
          width: 580px;
          color: #000;
          padding: 16px 10px !important;
          text-align: center;
        "
      >
        <div class="text-[18px] mb-[24px] px-[22px]">
          El importe de su producto es inferior a US$ 2000,
          <span class="text-[#e50113]"
            >los gastos de envío pueden ser más caros que el coste del
            producto</span
          >, se recomienda aumentar la compra a US$ 2000.
        </div>
        <div class="flex justify-between">
          <n-button
            round
            color="#fff"
            text-color="#000"
            data-spm-box="cart-to-checkout"
            @click="onConfirmSubmit($event, 'dialog')"
            class="border-btn w-[240px] h-[36px] text-[16px]"
          >
            {{ authStore.i18n("cm_common.buySubmit") }}</n-button
          >
          <n-button
            round
            color="#E50113"
            text-color="#fff"
            class="w-[240px] h-[36px] text-[16px]"
            @click="onCancelSubmit"
          >
            {{ authStore.i18n("cm_common.buyAgain") }}</n-button
          >
        </div>
      </n-card>
    </n-modal>
    <SubmitLoadingModal :show="pageData.submitLoading" />
  </div>
</template>
<script setup lang="ts">
import { useGetCart } from "@/composables/http";
import { useAuthStore } from "@/stores/authStore";

import SkuCard from "./components/SkuCard.vue";
import GoodsCard from "./components/GoodsCard.vue";
import SearchStepCard from "./components/SearchStepCard.vue";
import SubmitLoadingModal from "@/pages/find/components/SubmitLoadingModal.vue";

const message = useMessage();
const authStore = useAuthStore();
const loginRegister = inject<any>("loginRegister");

const pageData = reactive<any>({
  selectAll: false,
  dialogVisible: false,
  errorDialogVisible: false,
  submitDialogVisible: false,
  errorMessage: "",
  goodsList: <any>[],
  currentSku: <any>{},
  updatedSku: <any>{},
  currentGoods: <any>{},
  selectedSpec: <any>{},
  totalEstimateFreight: null, //预估运费总计（全部SKU可预估运费时，才返回）
  loading: false,
  submitLoading: false,
});

watch(
  () => pageData.selectedSpec,
  (newVal: any) => {
    // 多组规格时 选中倒数第二组的时候 需要检验sku的库存以及上下架状态
    if (
      newVal &&
      pageData.currentGoods?.specList?.length > 1 &&
      Object.values(newVal).length > pageData.currentGoods?.specList.length - 2
    ) {
      const targetId =
        pageData.currentGoods?.specList[
          pageData.currentGoods?.specList?.length - 2
        ].id;
      for (const id in pageData.selectedSpec) {
        if (targetId == id) {
          updateSpecStatus();
          break;
        }
      }
    } else {
      //只有一组时 直接校验sku的库存以及上下架状态
      updateOneSpecStatus();
    }
  },
  { deep: true }
);

onGetCart("init");
// 获取购物车列表
async function onGetCart(type?: string) {
  if (type == "init") {
    pageData.loading = true;
  }
  const res: any = await useGetCart({});
  if (type == "init") {
    pageData.loading = false;
  }
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    if (res?.data?.totalEstimateFreight) {
      pageData.totalEstimateFreight = res?.data.totalEstimateFreight;
    } else {
      pageData.totalEstimateFreight = null;
    }

    // 商品选中
    pageData.goodsList.forEach((goods: any) => {
      goods.selected = goods.skuList.every((obj: any) => obj.selected);
      goods.skuTotalQuantity = 0; //sku总数量
      goods.skuSelectedQuantity = 0; // 选中的sku总数量
      goods.skuList.forEach((sku: any) => {
        // sku上没有最小加购数量时，则取商品的最小加购数量
        if (!sku.minIncreaseQuantity) {
          sku.minIncreaseQuantity = goods.minIncreaseQuantity;
        }
        goods.skuTotalQuantity += sku.buyQty;
        if (sku.selected) {
          goods.skuSelected = true; // sku是否选中
          goods.skuSelectedQuantity += sku.buyQty;
        }
      });
    });
    // 全选
    pageData.selectAll = pageData.goodsList.every(
      (goods: any) => goods.selected
    );
    authStore.getCartList(res?.data); // 同步更新store的购物车数据
  } else if (res?.result?.code === 403) {
    loginRegister?.openLogin(); //未登录 去登录
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

// 全选
async function onAllSelection(value: any) {
  onUpdateCart({ selectedAll: value });
}
// 商品选中
function onGoodsSelection(value: any, goods: any) {
  onUpdateCart({ selected: value, goodsId: goods.goodsId, padc: goods.padc });
}
// sku选中
function onSkuSelection(value: any, sku: any, goods: any) {
  onUpdateCart({ selected: value, skuId: sku.skuId, padc: sku.padc });
}
// sku数量修改
function onCartQtyUpdate(value: any, sku: any, goods: any) {
  onUpdateCart({
    quantity: value,
    selected: sku.selected,
    skuId: sku.skuId,
    padc: sku.padc,
  });
}
// 修改
async function onUpdateCart(params: any) {
  const res: any = await useUpdateCart(params);
  if (res?.result?.code === 200) {
    onGetCart();
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
// 删除商品
async function onDeleteGoods(goods: any) {
  onRemoveCart({ goodsId: goods.goodsId, padc: goods.padc });
}
// 删除sku
async function onDeleteSku(sku: any, goods: any) {
  onRemoveCart({ skuId: sku.skuId, padc: sku.padc });
}
// 删除
async function onRemoveCart(params: any) {
  const res: any = await useRemoveCart(params);
  if (res?.result?.code === 200) {
    message.success(authStore.i18n("cm_find.deleteSuccessMessage"), {
      duration: 3000,
    });
    onGetCart();
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

function onGoFindSubmit(event: any) {
  window?.MyStat?.addPageEvent("click_start_looking", `点击创建询盘按钮`, true); // 埋点
  if (pageData.stat?.selectTotalSalePrice < 2000) {
    pageData.submitDialogVisible = true;
    window?.MyStat?.addPageEvent(
      "less_then_2000usd_dialog_open",
      `未满2000美元对话框-打开`
    ); // 埋点
    return;
  }
  onConfirmSubmit(event);
}

async function onConfirmSubmit(event: any, from?: any) {
  pageData.submitLoading = true;
  if (from) {
    window?.MyStat?.addPageEvent(
      "less_then_2000usd_dialog_ingore",
      `未满2000美元对话框-忽略`
    ); // 埋点
  }
  window?.MyStat?.addPageEvent(
    "carrito_click_inquiry",
    `购物车列表进入询盘提交页`
  ); // 埋点

  const selectedSkuList = <any>[];
  pageData.goodsList?.forEach((goods: any) => {
    goods.skuList.forEach((sku: any) => {
      if (sku.selected) {
        selectedSkuList.push({
          quantity: sku.buyQty,
          skuId: sku.skuId,
          spm: sku.spm,
          routeId: goods.routeId,
          padc: sku.padc,
        });
      }
    });
  });
  if (!!window?.fbq) {
    window?.fbq("track", "InitiateCheckout", {
      currency: "USD",
      num_items: selectedSkuList?.length,
      contents: selectedSkuList,
    });
  }
  if (!!window?.ttq) {
    window?.ttq?.track("InitiateCheckout", {
      currency: "USD",
      value: pageData.stat?.selectTotalSalePrice,
      content_type: "product",
      description: JSON.stringify(selectedSkuList),
    });
  }

  const res: any = await useGetInquiry({
    params: selectedSkuList,
    siteId: window.siteData.siteInfo.id,
  });
  if (res?.result?.code === 200) {
    const inquiryInfo = res?.data;
    await authStore.setInquiryInfo(inquiryInfo);
    navigateToPage(`/find/submit`, {}, false, event);
  } else if (res?.result?.code === 403) {
    loginRegister?.openLogin(); //未登录 去登录
  } else {
    window?.MyStat?.addPageEvent(
      "submit_start_looking_error",
      `进入询盘信息页错误：${res?.result?.message}`
    ); // 埋点
    pageData.errorDialogVisible = true;
    setTimeout(() => {
      pageData.errorDialogVisible = false;
    }, 3000);
    pageData.errorMessage =
      res?.result?.message || authStore.i18n("cm_find.errorMessage");
  }
  pageData.submitLoading = false;
}

function onCancelSubmit() {
  window?.MyStat?.addPageEvent(
    "less_then_2000usd_dialog_close",
    `未满2000美元对话框-关闭`
  ); // 埋点
  pageData.submitDialogVisible = false;
}

async function onOpenSkuDialog(sku: any, goods: any) {
  const res: any = await useGoodsInfo({
    id: goods.goodsId,
    padc: sku.padc,
    deviceType: 1,
  });
  if (res?.result?.code === 200) {
    pageData.currentSku = sku;
    pageData.updatedSku = sku;
    pageData.currentGoods = res?.data;
    await onInitGoodsData();
    pageData.dialogVisible = true;
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
async function onInitGoodsData() {
  pageData.selectedSpec = {};
  pageData.currentSku.specItemList.forEach((spec: any) => {
    pageData.selectedSpec[spec.groupId] = spec.itemId;
  });
  // 只有一个规格时 需要在初始化的时候 去判断有没有库存以及上下架
  if (pageData.currentGoods?.specList.length == 1) {
    const onlyOneSpecItems = pageData.currentGoods?.specList[0].items;
    onlyOneSpecItems.forEach((t: any) => {
      t.disabled = true;
      const matchingSkus = pageData.currentGoods?.skuList.filter((sku: any) => {
        return sku.specItems.some(
          (skuSpecItem: any) => skuSpecItem.itemId === t.itemId
        );
      });
      if (matchingSkus.length > 0) {
        matchingSkus.forEach((sku: any) => {
          let disabled = true;
          // 判断当前选中的sku是否是当前sku 如果是则还是可选的
          if (sku.id == pageData.currentSku?.skuId && sku.stockQty != 0) {
            disabled = false;
          }
          if (sku.stockQty != 0 && sku.cartQty == 0) {
            disabled = false;
          }
          const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
          onlyOneSpecItems.forEach((item: any) => {
            if (item.itemId == matchingItemId) {
              item.disabled = disabled;
            }
          });
        });
      } else {
        onlyOneSpecItems.forEach((item: any) => {
          item.disabled = true;
        });
      }
    });
  }
  onFilterSku();
}
function onSpecUpdate(specId: string, itemId: string, item: any) {
  if (item.disabled) return;
  pageData.selectedSpec[specId] = itemId;
}
// 更新最后一组规格组的状态 最后一组规格会校验sku的库存以及下架状态 如果没有查到sku 默认下架
function updateSpecStatus() {
  // 将对象转成数组 方便处理
  let selectList = <any>[];
  for (const id in pageData.selectedSpec) {
    selectList.push(pageData.selectedSpec[id]);
  }
  // 这里需要将选中的最后一组的规格给移除掉 再去找匹配的sku
  const lastSpecList =
    pageData.currentGoods.specList[pageData.currentGoods.specList.length - 1];
  lastSpecList.items.forEach((item: any) => {
    if (selectList.includes(item.itemId)) {
      const existingIndex = selectList.indexOf(item.itemId);
      if (existingIndex !== -1) {
        selectList.splice(existingIndex, 1);
      }
    }
  });
  lastSpecList.items.forEach((t: any) => {
    t.disabled = true;
  });
  // 获取所有满足条件的SKU
  const matchingSkus = pageData.currentGoods.skuList.filter((sku: any) => {
    return selectList.every((specItemId: string) =>
      sku.specItems.some(
        (skuSpecItem: any) => skuSpecItem.itemId === specItemId
      )
    );
  });
  // 如果有满足条件的SKU，则更新规格的可选状态
  if (matchingSkus.length > 0) {
    matchingSkus.forEach((sku: any) => {
      let disabled = true;
      // 判断当前选中的sku是否是当前sku 如果是则还是可选的
      if (sku.id == pageData.currentSku?.skuId && sku.stockQty != 0) {
        disabled = false;
      }
      // 如果库存为0 则不可选 如果cartQty选购的数量大于0 则是已加购的规格 也是不可选的
      if (sku.stockQty != 0 && sku.cartQty == 0) {
        disabled = false;
      }
      const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
      lastSpecList.items.forEach((item: any) => {
        if (item.itemId == matchingItemId) {
          item.disabled = disabled;
        }
      });
    });
  } else {
    lastSpecList.items.forEach((item: any) => {
      item.disabled = true;
    });
  }
  onFilterSku();
}
// 只有一个规格时 判断当前规格的sku有没有库存以及上下架
function updateOneSpecStatus() {
  const onlyOneSpecItems = pageData.currentGoods?.specList[0].items;
  onlyOneSpecItems.forEach((t: any) => {
    t.disabled = true;
    const matchingSkus = pageData.currentGoods?.skuList.filter((sku: any) => {
      return sku.specItems.some(
        (skuSpecItem: any) => skuSpecItem.itemId === t.itemId
      );
    });
    if (matchingSkus.length > 0) {
      matchingSkus.forEach((sku: any) => {
        let disabled = true;
        // 判断当前选中的sku是否是当前sku 如果是则还是可选的
        if (sku.id == pageData.currentSku?.skuId && sku.stockQty != 0) {
          disabled = false;
        }
        if (sku.stockQty != 0 && sku.cartQty == 0) {
          disabled = false;
        }
        const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
        onlyOneSpecItems.forEach((item: any) => {
          if (item.itemId == matchingItemId) {
            item.disabled = disabled;
          }
        });
      });
    } else {
      onlyOneSpecItems.forEach((item: any) => {
        item.disabled = true;
      });
    }
  });
  onFilterSku();
}
// 筛选出sku
function onFilterSku() {
  const lastSpecList =
    pageData.currentGoods.specList[pageData.currentGoods.specList.length - 1];
  // 选中的sku是下架或者没有库存的情况下 将最后一组规格里选中规格的取消
  for (const id in pageData.selectedSpec) {
    lastSpecList.items.forEach((item: any) => {
      if (item.itemId == pageData.selectedSpec[id] && item.disabled) {
        delete pageData.selectedSpec[id];
      }
    });
  }
  const skus = pageData.currentGoods.skuList.filter((sku: any) => {
    return (
      Object.entries(pageData.selectedSpec).every(([specId, itemId]) => {
        const matchingSpec = sku.specItems.find(
          (spec: any) => spec.specId === specId
        );
        if (!matchingSpec || matchingSpec.itemId !== itemId) {
          return false;
        }
        return true;
      }) && Object.keys(pageData.selectedSpec).length === sku.specItems.length
    );
  });
  pageData.updatedSku = skus.length > 0 ? skus[0] : null;
}

async function onConfirm() {
  const params = {
    selected: true,
    updatedSkuId: pageData.updatedSku.id,
    skuId: pageData.currentSku.skuId,
    goodsId: pageData.currentGoods.id,
    padc: pageData.currentSku.padc,
  };
  const res: any = await useUpdateCart(params);
  if (res?.result?.code === 200) {
    pageData.dialogVisible = false;
    onGetCart();
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
function onCancel() {
  pageData.dialogVisible = false;
}

function onGoHome() {
  window.location.href = "/";
}

// 保存选择的国家
const onSaveCountry = () => {
  // 可以根据需要决定是否重新加载页面
  window.location.reload();
};

onMounted(() => {
  window.addEventListener("scroll", handleAnchorScroll);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", handleAnchorScroll);
});

function handleAnchorScroll() {
  // 获取各个元素
  const footerElement = document.getElementById("page-footer");
  const formAffix = document.getElementById("submit-affix");

  if (footerElement && formAffix) {
    // 获取表单和页脚的高度
    const affixHeight = formAffix.offsetHeight;
    const footerHeight = footerElement.offsetHeight;
    // 判断文档高度是否小于(表单高度+页脚高度+80px误差)
    const criticalHeight = affixHeight + footerHeight + 80;
    // 文档高度足够，只需处理正常的固钉逻辑
    if (window.innerHeight >= criticalHeight) {
      formAffix.style.top = "20px";
    } else {
      const footerElement = document.getElementById("page-footer");
      const formAffix = document.getElementById("submit-affix");
      if (footerElement && formAffix) {
        const footerTop =
          footerElement.getBoundingClientRect().top + window.scrollY;
        const scrollTop =
          window.scrollY ||
          document.documentElement.scrollTop ||
          document.body.scrollTop ||
          0;
        const windowHeight =
          window.innerHeight ||
          document.documentElement.clientHeight ||
          document.body.clientHeight;

        // 计算距离底部的距离
        const distanceFromFooter = footerTop - (scrollTop + windowHeight);

        // 判断是否滚动到page-footer元素的位置
        if (distanceFromFooter <= 0) {
          // 计算right-affix的top值
          formAffix.style.top = distanceFromFooter + "px";
        } else {
          // 如果没有滚动到footer的位置，保持form-affix的初始top值
          formAffix.style.top = 20 + "px";
        }
      }
    }
  }
}
</script>
<style scoped lang="scss">
.container {
  height: auto;
  margin: 0 auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 90vh;
}

.sku-checkbox {
  background: #f2f2f2;
  padding: 6px 4px 6px 10px;
  .sku-container {
    padding: 0 !important;
  }
}
:deep(.n-checkbox.n-checkbox--checked .n-checkbox-box) {
  background: #e50113;
}

:deep(.n-checkbox__label) {
  width: 100%;
  padding: 0 !important;
}

:deep(.divider .n-divider) {
  margin: 0px;
}
:deep(.n-divider__title) {
  margin-left: 8px !important;
  margin-right: 8px !important;
}
.divider.n-divider:not(.n-divider--vertical) {
  margin: 0px;
}
:deep(.divider .n-divider__line.n-divider__line--left) {
  display: none;
}
.spec-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 2.5rem;
  margin: 0.4rem 1rem 0.4rem 0;
  padding: 0 1.1rem;
  min-width: 40px;
  cursor: pointer;
  color: #797979;
  border-radius: 5px;
  border: 1px solid #d7d7d7;
}
.disabled-btn {
  background: #d7d7d7;
  cursor: not-allowed;
  border: none;
}
.current-btn {
  color: #e50113;
  cursor: pointer;
  border: none;
  background: rgba(229, 1, 19, 0.18);
  .btn-tick {
    opacity: 1;
  }
}
.btn-tick {
  position: absolute;
  right: -10px;
  bottom: 0px;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.btn-tick::before {
  content: ""; /* 添加伪类元素的内容 */
  position: absolute; /* 将伪类元素相对于父元素定位 */
  top: 5px;
  right: 2px;
  bottom: 1px;
  left: 1px;
  background-color: #e50113; /* 设置背景色 */
  z-index: -1; /* 将伪类元素放在父元素的下方 */
  border-radius: 8px 0 5px 0;
}
.border-btn {
  border: 1px solid #c7c7c7;
}
.border-btn:hover {
  background: #e50113;
  color: #fff;
  border: none;
}

.loading-overlay {
  position: fixed;
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
}

:deep(.country-delivery) {
  font-size: 16px;
  margin-right: 10px;
}
:deep(.country-code) {
  font-size: 16px;
}
:deep(.popover-country) {
  width: 100%;
  padding: 0;
  justify-content: space-between;
  .country-code {
    line-height: 16px !important;
  }
}
</style>
